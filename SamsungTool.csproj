﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
	<PublishAot>true</PublishAot>
    <EnableTrimming>true</EnableTrimming>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Version>*******</Version>
    <ApplicationIcon>Resources\logo.ico</ApplicationIcon>
    <Win32Resource />
    
    <!-- AOT and VMProtect optimizations -->
    <TrimMode>link</TrimMode>
    <TrimmerDefaultAction>link</TrimmerDefaultAction>
    <IlcOptimizationPreference>Speed</IlcOptimizationPreference>
    <IlcInstructionSet>x86-x64</IlcInstructionSet>
    <IlcFoldIdenticalMethodBodies>true</IlcFoldIdenticalMethodBodies>
    <StripSymbols>true</StripSymbols>
    
    <!-- Resource handling improvements -->
    <UseAppHost>true</UseAppHost>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>
  </PropertyGroup>
  
  <!-- Assembly preservation for reflection -->
  <ItemGroup>
    <TrimmerRootAssembly Include="System.Reflection" />
    <TrimmerRootAssembly Include="System.IO.Ports" />
    <TrimmerRootAssembly Include="System.Diagnostics.Process" />
    <TrimmerRootAssembly Include="System.Security.Cryptography" />
    <TrimmerRootAssembly Include="System.Management" />
  </ItemGroup>
  
  <!-- Preserve specific methods for AOT -->
  <ItemGroup>
    <IlcArg Include="--reflectiondata" />
    <IlcArg Include="--stack-trace-data" />
    <IlcArg Include="--methodbodyfolding" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Resources\adb.exe" />
    <None Remove="Resources\logo.ico" />
  </ItemGroup>
  <ItemGroup>
  <EmbeddedResource Include="Resources\adb.exe" />
  <EmbeddedResource Include="Resources\retails.apk" />
  <EmbeddedResource Include="Resources\retails_OldSDK.apk" />
  <EmbeddedResource Include="Resources\Fota.apk" />
  <AvaloniaResource Include="Resources\logo.ico" />
</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.0" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.0" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.0" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.0" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.3.0">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
	  
    <PackageReference Include="runtime.native.System.IO.Ports" Version="9.0.5" />
    <PackageReference Include="SharpOdinClient" Version="1.0.2" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
    <PackageReference Include="System.IO.Ports" Version="9.0.5" />
	<PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
	<PackageReference Include="System.ServiceProcess.ServiceController" Version="9.0.5" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Include="System.Management" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Views\InputSerial.axaml.cs">
      <DependentUpon>InputSerial.axaml</DependentUpon>
    </Compile>
    <Compile Update="Views\InputProdcode.axaml.cs">
      <DependentUpon>InputProdcode.axaml</DependentUpon>
    </Compile>
  </ItemGroup>
</Project>