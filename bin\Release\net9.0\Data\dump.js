Java.perform(function() {
    console.log("Hooking input functions...");
    
    // Hook read syscall
    var read_addr = Module.findExportByName("libc.so", "read");
    if (read_addr) {
        Interceptor.attach(read_addr, {
            onEnter: function(args) {
                this.fd = args[0].toInt32();
                this.buf = args[1]; 
                this.size = args[2].toInt32();
                if (this.fd == 0) {  // stdin
                    console.log("[READ] Reading " + this.size + " bytes from stdin");
                }
            },
            onLeave: function(retval) {
                if (this.fd == 0 && retval.toInt32() > 0) {
                    var data = Memory.readUtf8String(this.buf, retval.toInt32());
                    console.log("[READ] Input: '" + data + "'");
                }
            }
        });
    }
    
    // Hook string comparison functions
    ["strcmp", "strncmp", "memcmp"].forEach(function(fname) {
        var addr = Module.findExportByName("libc.so", fname);
        if (addr) {
            Interceptor.attach(addr, {
                onEnter: function(args) {
                    try {
                        var s1 = Memory.readUtf8String(args[0]);
                        var s2 = Memory.readUtf8String(args[1]);
                        console.log("[" + fname + "] '" + s1 + "' vs '" + s2 + "'");
                    } catch(e) {
                        console.log("[" + fname + "] comparison (binary data)");
                    }
                },
                onLeave: function(retval) {
                    console.log("[" + fname + "] result: " + retval);
                }
            });
        }
    });
    
    // Search for strings in memory
    var base = ptr("0x7f0d4f8000");
    var size = 0x77000;
    
    console.log("Searching for potential response strings...");
    var patterns = ["OK", "SUCCESS", "VALID", "AUTHORIZED", "YES", "NO", "FAIL"];
    
    patterns.forEach(function(pattern) {
        try {
            var result = Memory.scanSync(base, size, pattern);
            if (result.length > 0) {
                result.forEach(function(match) {
                    console.log("Found '" + pattern + "' at " + match.address);
                });
            }
        } catch(e) {}
    });
});