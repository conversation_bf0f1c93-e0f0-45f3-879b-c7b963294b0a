is_global = true
build_property.EnableAotAnalyzer = true
build_property.EnableSingleFileAnalyzer = true
build_property.EnableTrimAnalyzer = true
build_property.IncludeAllContentForSelfExtract = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SamsungTool
build_property.ProjectDir = D:\SamsungTool_refactored_20250604173140\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/SamsungTool_refactored_20250604173140/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/HistoryWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/InputProdcode.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/InputSerial.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/LoginWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/QRWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/SamsungTool_refactored_20250604173140/Views/TokenExpiredWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
