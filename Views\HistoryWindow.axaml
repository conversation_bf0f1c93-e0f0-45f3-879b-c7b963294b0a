<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:models="using:SamsungTool.Models"
        x:Class="SamsungTool.Views.HistoryWindow"
        Title="Log History - Samsung Tool"
        Width="900" Height="600"
        MinWidth="700" MinHeight="450"
        WindowStartupLocation="CenterOwner"
        TransparencyLevelHint="AcrylicBlur"
        Background="#1E1E1E"
        Icon="avares://SamsungTool/Resources/logo.ico">

	<Window.Styles>
		<Style Selector="ListBoxItem">
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="Padding" Value="8"/>
			<Setter Property="Margin" Value="2"/>
			<Setter Property="CornerRadius" Value="5"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="Transparent"/>
		</Style>

		<Style Selector="ListBoxItem:pointerover">
			<Setter Property="Background" Value="#2D2D2D"/>
			<Setter Property="BorderBrush" Value="#404040"/>
		</Style>

		<Style Selector="ListBoxItem:selected">
			<Setter Property="Background" Value="#3D3D3D"/>
			<Setter Property="BorderBrush" Value="#0078D4"/>
		</Style>

		<Style Selector="Button.action-btn">
			<Setter Property="Background" Value="#404040"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#606060"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="5"/>
			<Setter Property="Padding" Value="12,6"/>
			<Setter Property="Margin" Value="3"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="12"/>
		</Style>

		<Style Selector="Button.action-btn:pointerover">
			<Setter Property="Background" Value="#505050"/>
			<Setter Property="BorderBrush" Value="#707070"/>
		</Style>

		<Style Selector="Button.clear-all-btn">
			<Setter Property="Background" Value="#DC3545"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#C82333"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="5"/>
			<Setter Property="Padding" Value="12,6"/>
			<Setter Property="Margin" Value="3"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="12"/>
		</Style>

		<Style Selector="Button.clear-all-btn:pointerover">
			<Setter Property="Background" Value="#C82333"/>
			<Setter Property="BorderBrush" Value="#BD2130"/>
		</Style>

		<Style Selector="Button.clear-all-btn:pressed">
			<Setter Property="Background" Value="#BD2130"/>
		</Style>
		
		<Style Selector="TextBlock.header">
			<Setter Property="FontSize" Value="16"/>
			<Setter Property="FontWeight" Value="Bold"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="Margin" Value="0,0,0,8"/>
		</Style>
		
		<Style Selector="TextBlock.subheader">
			<Setter Property="FontSize" Value="14"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="Foreground" Value="#CCCCCC"/>
			<Setter Property="Margin" Value="0,0,0,4"/>
		</Style>
		
		<Style Selector="TextBlock.detail">
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="Foreground" Value="#AAAAAA"/>
		</Style>
		
		<Style Selector="ScrollViewer.log-content">
			<Setter Property="Background" Value="#0F0F0F"/>
			<Setter Property="BorderBrush" Value="#404040"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="8"/>
		</Style>
	</Window.Styles>

	<Grid RowDefinitions="Auto,*" Margin="16">
		<!-- Header -->
		<Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,16">
			<StackPanel Grid.Column="0">
				<TextBlock Text="Log History" Classes="header"/>
				<TextBlock x:Name="txtHistoryInfo" Text="Loading..." Classes="detail"/>
			</StackPanel>

			<StackPanel Grid.Column="1" Orientation="Horizontal">
				<Button x:Name="btnClearAll" Content="Clear All" Classes="clear-all-btn"/>
			</StackPanel>
		</Grid>

		<!-- Main Content -->
		<Grid Grid.Row="1" ColumnDefinitions="320,*">
			<!-- History List -->
			<Border Grid.Column="0" Background="#252525" CornerRadius="8" Padding="12" Margin="0,0,10,0">
				<Grid RowDefinitions="Auto,*">
					<TextBlock Grid.Row="0" Text="Sessions" Classes="subheader" Margin="0,0,0,10"/>

					<ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
						<ListBox x:Name="lstHistory" Background="Transparent" BorderThickness="0">
							<ListBox.ItemTemplate>
								<DataTemplate x:DataType="models:LogEntryViewModel">
									<Grid RowDefinitions="Auto,Auto,Auto,Auto" Margin="0,2">
										<TextBlock Grid.Row="0" Text="{Binding OperationName}"
												   FontWeight="Bold" FontSize="13" Foreground="White" Margin="0,0,0,3"/>
										<TextBlock Grid.Row="1" Text="{Binding StartTimeString}"
												   FontSize="11" Foreground="#CCCCCC" Margin="0,0,0,2"/>
										<StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,3">
											<Border Background="{Binding StatusBrush}" CornerRadius="2" Padding="4,1">
												<TextBlock Text="{Binding StatusDisplay}" FontSize="9"
														   FontWeight="SemiBold" Foreground="White"/>
											</Border>
											<TextBlock Text="{Binding DurationString}" FontSize="10"
													   Foreground="#AAAAAA" Margin="6,0,0,0" VerticalAlignment="Center"/>
										</StackPanel>
										<Rectangle Grid.Row="3" Height="1" Fill="#404040" Margin="0,6,0,0"/>
									</Grid>
								</DataTemplate>
							</ListBox.ItemTemplate>
						</ListBox>
					</ScrollViewer>
				</Grid>
			</Border>

			<!-- Log Detail -->
			<Border Grid.Column="1" Background="#252525" CornerRadius="8" Padding="16">
				<Grid RowDefinitions="Auto,*,Auto">
					<!-- Detail Header -->
					<Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,12">
						<StackPanel Grid.Column="0">
							<TextBlock x:Name="txtDetailOperation" Text="Select a log session"
									   Classes="subheader"/>
							<TextBlock x:Name="txtDetailInfo" Text="" Classes="detail"/>
						</StackPanel>

						<StackPanel Grid.Column="1" Orientation="Horizontal">
							<Button x:Name="btnDeleteEntry" Content="Delete" Classes="action-btn"
									IsEnabled="False"/>
						</StackPanel>
					</Grid>

					<!-- Log Content -->
					<ScrollViewer Grid.Row="1" x:Name="logContentScroll" Classes="log-content"
								  VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
								  Margin="0,0,0,8">
						<SelectableTextBlock x:Name="txtLogContent"
											 FontFamily="Consolas,Monaco,monospace"
											 FontSize="12"
											 Foreground="#DDDDDD"
											 Text="Select a log session to view its content."
											 TextWrapping="Wrap"
											 LineHeight="16"
											 Margin="0,0,0,16"/>
					</ScrollViewer>

					<!-- Bottom spacer to ensure content is not cut off -->
					<Border Grid.Row="2" Height="8"/>
				</Grid>
			</Border>
		</Grid>


	</Grid>
</Window>
