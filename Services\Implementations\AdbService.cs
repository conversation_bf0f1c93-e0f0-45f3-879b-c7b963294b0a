using SamsungTool.Services.Implementations.Adb;
using SamsungTool.Services.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Services.Implementations
{
    public class AdbService : IAdbService, IDisposable
    {
        private bool _disposed = false;
        private readonly AdbReadInfoService _readInfoService;

        public AdbService()
        {
            _readInfoService = new AdbReadInfoService();
        }

        public async Task<bool> InitializeAsync(CancellationToken cancellationToken = default)
        {
            await _readInfoService.Run(cancellationToken);
            return true;
        }

        public async Task<bool> CheckServerAsync()
        {
            await _readInfoService.Run();
            return true;
        }

        public async Task<bool> FindDeviceAsync(CancellationToken cancellationToken = default)
        {
            await _readInfoService.Run(cancellationToken);
            return true;
        }

        public async Task<bool> ReadDeviceInfoAsync(CancellationToken cancellationToken = default)
        {
            await _readInfoService.Run(cancellationToken);
            return true;
        }

        public async Task ExecuteOperationAsync(string operation, CancellationToken cancellationToken = default)
        {
            switch (operation)
            {
                case "readinfo":
                    var readInfoService = new AdbReadInfoService();
                    await readInfoService.Run(cancellationToken);
                    break;
                case "frp":
                    var frpService = new AdbFrpService();
                    await frpService.Run(cancellationToken);
                    break;
                case "rebootdownload":
                    var rebootService = new AdbRebootService();
                    await rebootService.RunDownloadMode(cancellationToken);
                    break;
                case "rebootrecovery":
                    var rebootRecoveryService = new AdbRebootService();
                    await rebootRecoveryService.RunRecoveryMode(cancellationToken);
                    break;
                case "kgos14":
                    var kgOsService = new AdbKgOsService();
                    await kgOsService.Run(cancellationToken);
                    break;
                case "kgapps":
                    var kgAppsService = new AdbKgAppsService();
                    await kgAppsService.Run(cancellationToken);
                    break;
                case "changecsc":
                    var cscService = new AdbChangeCSCService();
                    await cscService.Run(cancellationToken);
                    break;
                case "changeserial":
                    var serialService = new AdbChangeSerialService();
                    await serialService.Run(cancellationToken);
                    break;
                default:
                    throw new ArgumentException($"Unknown operation: {operation}");
            }
        }

        public async Task KgRemove2025Async(CancellationToken cancellationToken = default)
        {
            var service = new AdbKgRemoveService();
            await service.Run(cancellationToken);
        }

        public async Task ExecuteServerBinaryOperationAsync(string operationName, string jobName, bool requiresInitialSetup = true, CancellationToken cancellationToken = default)
        {
            var service = new AdbKgRemoveService();
            await service.Run(cancellationToken);
        }

        public async Task ExecuteRawShellCommandsAsync(string commandsText, CancellationToken cancellationToken = default)
        {
            var service = new AdbRawShellService();
            await service.Run(commandsText, cancellationToken);
        }

        public async Task DisableServiceAsync(CancellationToken cancellationToken = default)
        {
            var service = new AdbDisableServiceService();
            await service.Run(cancellationToken);
        }

        public string GetSerialNumber()
        {
            // TODO: Implement serial number retrieval logic
            return string.Empty;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // Dispose managed resources if any
            }

            _disposed = true;
        }

        ~AdbService()
        {
            Dispose(false);
        }
    }
}