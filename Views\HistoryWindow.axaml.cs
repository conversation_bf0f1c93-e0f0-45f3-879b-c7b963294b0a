using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Media;
using Avalonia.Threading;
using SamsungTool.Models;
using SamsungTool.Services;
using SamsungTool.Services.Interfaces;

namespace SamsungTool.Views
{
    public partial class HistoryWindow : Window
    {
        private readonly ILogManager _logManager;
        private LogEntryCollection _logHistory = new();
        private LogEntry? _selectedLogEntry;
        private List<LogEntryViewModel> _logViewModels = new();

        public HistoryWindow()
        {
            InitializeComponent();
            _logManager = ServiceLocator.GetService<ILogManager>();
            
            RegisterEventHandlers();
            _ = LoadHistoryAsync();
        }

        private void RegisterEventHandlers()
        {
            if (btnClearAll != null) btnClearAll.Click += BtnClearAll_Click;
            if (btnDeleteEntry != null) btnDeleteEntry.Click += BtnDeleteEntry_Click;

            if (lstHistory != null)
                lstHistory.SelectionChanged += LstHistory_SelectionChanged;
        }

        private async Task LoadHistoryAsync()
        {
            try
            {
                UpdateStatus("Loading history...");
                
                _logHistory = await _logManager.GetLogHistoryAsync();
                
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    if (lstHistory != null)
                    {
                        // Sort by start time descending (newest first) and create ViewModels
                        _logViewModels = _logHistory.Entries
                            .OrderByDescending(e => e.StartTime)
                            .Select(e => new LogEntryViewModel(e))
                            .ToList();

                        lstHistory.ItemsSource = _logViewModels;
                    }

                    UpdateHistoryInfo();
                    UpdateStatus($"Loaded {_logHistory.Entries.Count} log entries");
                });
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error loading history: {ex.Message}");
            }
        }

        private void UpdateHistoryInfo()
        {
            if (txtHistoryInfo == null) return;
            
            var totalEntries = _logHistory.Entries.Count;
            var successCount = _logHistory.GetByStatus(LogEntryStatus.Success).Count;
            var failedCount = _logHistory.GetByStatus(LogEntryStatus.Failed).Count;
            var cancelledCount = _logHistory.GetByStatus(LogEntryStatus.Cancelled).Count;
            
            txtHistoryInfo.Text = $"{totalEntries} total • {successCount} success • {failedCount} failed • {cancelledCount} cancelled";
        }

        private void UpdateStatus(string message)
        {
            // Status bar removed for cleaner UI
        }



        private async void BtnClearAll_Click(object? sender, RoutedEventArgs e)
        {
            var result = await ShowConfirmAsync("Clear All History", 
                "Are you sure you want to clear all log history?\n\nThis will delete all log files and cannot be undone.");
                
            if (result)
            {
                try
                {
                    UpdateStatus("Clearing history...");
                    var success = await _logManager.ClearAllHistoryAsync();
                    
                    if (success)
                    {
                        await LoadHistoryAsync();
                        UpdateStatus("History cleared successfully");
                    }
                    else
                    {
                        UpdateStatus("Failed to clear history");
                        await ShowMessageAsync("Clear Failed", "Failed to clear history. Please try again.");
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatus($"Clear error: {ex.Message}");
                    await ShowMessageAsync("Clear Error", $"An error occurred while clearing history:\n{ex.Message}");
                }
            }
        }



        private async void BtnDeleteEntry_Click(object? sender, RoutedEventArgs e)
        {
            if (_selectedLogEntry == null) return;

            var result = await ShowConfirmAsync("Delete Log Entry", 
                $"Are you sure you want to delete this log entry?\n\nOperation: {_selectedLogEntry.OperationName}\nDate: {_selectedLogEntry.StartTimeString}\n\nThis cannot be undone.");
                
            if (result)
            {
                try
                {
                    UpdateStatus("Deleting log entry...");
                    var success = await _logManager.DeleteLogEntryAsync(_selectedLogEntry.Id);
                    
                    if (success)
                    {
                        await LoadHistoryAsync();
                        ClearLogDetail();
                        UpdateStatus("Log entry deleted successfully");
                    }
                    else
                    {
                        UpdateStatus("Failed to delete log entry");
                        await ShowMessageAsync("Delete Failed", "Failed to delete log entry. Please try again.");
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatus($"Delete error: {ex.Message}");
                    await ShowMessageAsync("Delete Error", $"An error occurred while deleting:\n{ex.Message}");
                }
            }
        }



        private async void LstHistory_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (lstHistory?.SelectedItem is LogEntryViewModel selectedViewModel)
            {
                _selectedLogEntry = selectedViewModel.LogEntry;
                await LoadLogDetail(selectedViewModel.LogEntry);

                if (btnDeleteEntry != null) btnDeleteEntry.IsEnabled = true;
            }
            else
            {
                ClearLogDetail();
                if (btnDeleteEntry != null) btnDeleteEntry.IsEnabled = false;
            }
        }

        private async Task LoadLogDetail(LogEntry logEntry)
        {
            try
            {
                UpdateStatus("Loading log content...");
                
                if (txtDetailOperation != null)
                    txtDetailOperation.Text = logEntry.OperationName;
                    
                if (txtDetailInfo != null)
                {
                    txtDetailInfo.Text = $"{logEntry.OperationType} • {logEntry.StartTimeString} • {logEntry.StatusDisplay} • {logEntry.DurationString}";
                }

                string logContent;
                if (!string.IsNullOrEmpty(logEntry.FilePath) && File.Exists(logEntry.FilePath))
                {
                    logContent = await _logManager.ReadLogFileAsync(logEntry.FilePath);
                }
                else
                {
                    logContent = !string.IsNullOrEmpty(logEntry.PlainTextContent) 
                        ? logEntry.PlainTextContent 
                        : "Log content not available.";
                }

                if (txtLogContent != null)
                    txtLogContent.Text = logContent;
                    
                UpdateStatus($"Loaded log for {logEntry.OperationName}");
            }
            catch (Exception ex)
            {
                if (txtLogContent != null)
                    txtLogContent.Text = $"Error loading log content: {ex.Message}";
                    
                UpdateStatus($"Error loading log: {ex.Message}");
            }
        }

        private void ClearLogDetail()
        {
            _selectedLogEntry = null;

            if (txtDetailOperation != null)
                txtDetailOperation.Text = "Select a log session";

            if (txtDetailInfo != null)
                txtDetailInfo.Text = "";

            if (txtLogContent != null)
                txtLogContent.Text = "Select a log session to view its content.";
        }



        private async Task ShowMessageAsync(string title, string message)
        {
            var messageBox = new Window
            {
                Title = title,
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                CanResize = false
            };

            var panel = new StackPanel { Margin = new Avalonia.Thickness(20) };
            var textBlock = new TextBlock { Text = message, TextWrapping = TextWrapping.Wrap };
            var button = new Button { Content = "OK", HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center, Margin = new Avalonia.Thickness(0, 20, 0, 0) };

            button.Click += (s, e) => messageBox.Close();

            panel.Children.Add(textBlock);
            panel.Children.Add(button);
            messageBox.Content = panel;

            await messageBox.ShowDialog(this);
        }

        private async Task<bool> ShowConfirmAsync(string title, string message)
        {
            var result = false;
            var messageBox = new Window
            {
                Title = title,
                Width = 450,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                CanResize = false
            };

            var panel = new StackPanel { Margin = new Avalonia.Thickness(20) };
            var textBlock = new TextBlock { Text = message, TextWrapping = TextWrapping.Wrap };
            var buttonPanel = new StackPanel 
            { 
                Orientation = Avalonia.Layout.Orientation.Horizontal, 
                HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                Margin = new Avalonia.Thickness(0, 20, 0, 0)
            };

            var yesButton = new Button { Content = "Yes", Margin = new Avalonia.Thickness(0, 0, 10, 0) };
            var noButton = new Button { Content = "No" };

            yesButton.Click += (s, e) => { result = true; messageBox.Close(); };
            noButton.Click += (s, e) => { result = false; messageBox.Close(); };

            buttonPanel.Children.Add(yesButton);
            buttonPanel.Children.Add(noButton);
            panel.Children.Add(textBlock);
            panel.Children.Add(buttonPanel);
            messageBox.Content = panel;

            await messageBox.ShowDialog(this);
            return result;
        }
    }
}
