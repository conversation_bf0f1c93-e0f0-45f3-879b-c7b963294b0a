using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using System;

namespace SamsungTool.Views
{
    public partial class TokenExpiredWindow : Window
    {
        private DispatcherTimer? _countdownTimer;
        private int _secondsRemaining = 5;
        private TextBlock? _countdownText;

        public TokenExpiredWindow()
        {
            InitializeComponent();
            this.Loaded += OnLoaded;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void OnLoaded(object? sender, RoutedEventArgs e)
        {
            var btnOk = this.FindControl<Button>("btnOk");
            if (btnOk != null)
            {
                btnOk.Click += BtnOk_Click;
            }

            _countdownText = this.FindControl<TextBlock>("countdownText");
            
            // Start countdown timer
            StartCountdownTimer();
        }

        private void StartCountdownTimer()
        {
            _countdownTimer = new DispatcherTimer();
            _countdownTimer.Interval = TimeSpan.FromSeconds(1);
            _countdownTimer.Tick += CountdownTimer_Tick;
            _countdownTimer.Start();
        }

        private void CountdownTimer_Tick(object? sender, EventArgs e)
        {
            _secondsRemaining--;

            if (_countdownText != null)
            {
                _countdownText.Text = $"Closing automatically in {_secondsRemaining} seconds...";
            }

            if (_secondsRemaining <= 0)
            {
                _countdownTimer?.Stop();
                // Force close the application
                Environment.Exit(0);
            }
        }

        private void BtnOk_Click(object? sender, RoutedEventArgs e)
        {
            _countdownTimer?.Stop();
            // Force close the application
            Environment.Exit(0);
        }

        protected override void OnClosed(EventArgs e)
        {
            _countdownTimer?.Stop();
            base.OnClosed(e);
        }
    }
} 