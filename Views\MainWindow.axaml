<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="SamsungTool.MainWindow"
        Title="Samsung Tool v2.0.4 [ Build 2025.06.26 ]"
        Width="1320" Height="780"
        MinWidth="1024" MinHeight="600"
        WindowStartupLocation="CenterScreen"
        TransparencyLevelHint="AcrylicBlur"
        Background="#1E1E1E"
        Icon="avares://SamsungTool/Resources/logo.ico">

	<Window.Styles>
		<!-- Enhanced Sidebar with Animation -->
		<Style Selector="Border.sidebar">
			<Setter Property="Background" Value="#252525"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="BorderThickness" Value="0,0,1,0"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-50px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border.sidebar.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced Logo with Animation -->
		<Style Selector="TextBlock.logo">
			<Setter Property="FontWeight" Value="Bold"/>
			<Setter Property="FontSize" Value="17"/>
			<Setter Property="Foreground" Value="#A83232"/>
			<Setter Property="LetterSpacing" Value="1"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.8) translateY(-10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:1.0" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:1.0" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.logo.loaded">
			<Setter Property="Opacity" Value="0.95"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<!-- Enhanced Sidebar Tabs with Animation -->
		<Style Selector="ListBox.sidebar-tabs">
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="Padding" Value="0"/>
			<Setter Property="Margin" Value="0"/>
			<Setter Property="BorderThickness" Value="0"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(20px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="ListBox.sidebar-tabs.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
		</Style>

		<Style Selector="ListBox.sidebar-tabs ListBoxItem">
			<Setter Property="Padding" Value="12"/>
			<Setter Property="Margin" Value="0 1"/>
			<Setter Property="CornerRadius" Value="5"/>
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="BorderThickness" Value="0"/>
			<Setter Property="Cursor" Value="Hand"/>
			<Setter Property="Height" Value="42"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="ListBox.sidebar-tabs ListBoxItem:pointerover /template/ ContentPresenter">
			<Setter Property="Background" Value="#404040"/>
		</Style>

		<Style Selector="ListBox.sidebar-tabs ListBoxItem:pointerover">
			<Setter Property="RenderTransform" Value="translateX(3px)"/>
		</Style>

		<Style Selector="ListBox.sidebar-tabs ListBoxItem:selected /template/ ContentPresenter">
			<Setter Property="Background" Value="#A83232"/>
		</Style>

		<Style Selector="ListBox.sidebar-tabs ListBoxItem:selected">
			<Setter Property="RenderTransform" Value="translateX(5px)"/>
		</Style>

		<!-- Enhanced Content Title with Animation -->
		<Style Selector="TextBlock.content-title">
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="15"/>
			<Setter Property="Foreground" Value="#F0F0F0"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-20px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.content-title.loaded">
			<Setter Property="Opacity" Value="0.92"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced Group Header with Animation -->
		<Style Selector="TextBlock.group-header">
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="Foreground" Value="#A83232"/>
			<Setter Property="LetterSpacing" Value="0.5"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-15px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.5" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.5" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBlock.group-header.loaded">
			<Setter Property="Opacity" Value="0.9"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<!-- Enhanced Card with Animation -->
		<Style Selector="Border.card">
			<Setter Property="Background" Value="#252525"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Padding" Value="12"/>
			<Setter Property="BoxShadow" Value="0 4 8 0 #30000000"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.95) translateY(15px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<BoxShadowsTransition Property="BoxShadow" Duration="0:0:0.3"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border.card.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<Style Selector="Border.card:pointerover">
			<Setter Property="BoxShadow" Value="0 8 16 0 #40000000"/>
			<Setter Property="RenderTransform" Value="scale(1.02) translateY(-2px)"/>
		</Style>

		<Style Selector="Border.separator">
			<Setter Property="Background" Value="#3F3F3F"/>
			<Setter Property="Height" Value="1"/>
			<Setter Property="HorizontalAlignment" Value="Stretch"/>
			<Setter Property="Margin" Value="0,2"/>
		</Style>

		<!-- Enhanced Button with Animation -->
		<Style Selector="Button">
			<Setter Property="Background" Value="#333333"/>
			<Setter Property="Foreground" Value="#E8E8E8"/>
			<Setter Property="BorderBrush" Value="#505050"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Padding" Value="12,6"/>
			<Setter Property="HorizontalContentAlignment" Value="Center"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="FontWeight" Value="Normal"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="Height" Value="32"/>
			<Setter Property="MinWidth" Value="80"/>
			<Setter Property="Cursor" Value="Hand"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
					<BrushTransition Property="Foreground" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.1"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button:pointerover /template/ ContentPresenter">
			<Setter Property="Background" Value="#404040"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>

		<Style Selector="Button:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.05)"/>
		</Style>

		<Style Selector="Button:pressed /template/ ContentPresenter">
			<Setter Property="Background" Value="#A83232"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>

		<Style Selector="Button:pressed">
			<Setter Property="RenderTransform" Value="scale(0.95)"/>
		</Style>

		<Style Selector="Button:disabled /template/ ContentPresenter">
			<Setter Property="Background" Value="#2A2A2A"/>
			<Setter Property="Foreground" Value="#808080"/>
			<Setter Property="BorderBrush" Value="#3A3A3A"/>
			<Setter Property="Opacity" Value="0.5"/>
		</Style>

		<Style Selector="Button.accent">
			<Setter Property="Background" Value="#A83232"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
			<Setter Property="FontWeight" Value="Medium"/>
		</Style>

		<Style Selector="Button.accent:pointerover /template/ ContentPresenter">
			<Setter Property="Background" Value="#B83838"/>
			<Setter Property="BorderBrush" Value="#B83838"/>
		</Style>

		<!-- Enhanced Function Button with Animation -->
		<Style Selector="Button.function">
			<Setter Property="Height" Value="38"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="Padding" Value="8,4"/>
			<Setter Property="HorizontalAlignment" Value="Stretch"/>
			<Setter Property="Margin" Value="2"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scale(0.9) translateY(10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.4" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.4" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button.function.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scale(1.0) translateY(0px)"/>
		</Style>

		<Style Selector="Button.function:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.03) translateY(-1px)"/>
		</Style>

		<Style Selector="Button.compact">
			<Setter Property="Padding" Value="8,4"/>
			<Setter Property="FontSize" Value="11"/>
			<Setter Property="Height" Value="26"/>
			<Setter Property="MinWidth" Value="60"/>
		</Style>

		<!-- Enhanced File Browse Button with Animation -->
		<Style Selector="Button.file-browse">
			<Setter Property="Width" Value="32"/>
			<Setter Property="Height" Value="28"/>
			<Setter Property="Padding" Value="4"/>
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="FontWeight" Value="Bold"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Button.file-browse:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.1) rotate(5deg)"/>
		</Style>

		<Style Selector="Button.file-browse:pressed">
			<Setter Property="RenderTransform" Value="scale(0.9) rotate(-2deg)"/>
		</Style>

		<Style Selector="Button.cancel-button">
			<Setter Property="Background" Value="#B83535"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#A02E2E"/>
			<Setter Property="Height" Value="34"/>
			<Setter Property="FontWeight" Value="Medium"/>
			<Setter Property="Opacity" Value="0.92"/>
		</Style>

		<Style Selector="Button.cancel-button:pointerover /template/ ContentPresenter">
			<Setter Property="Background" Value="#A02E2E"/>
			<Setter Property="BorderBrush" Value="#882626"/>
			<Setter Property="Opacity" Value="1"/>
		</Style>

		<!-- Enhanced TextBox with Animation -->
		<Style Selector="TextBox">
			<Setter Property="Background" Value="#222222"/>
			<Setter Property="Foreground" Value="#E8E8E8"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="Padding" Value="8,5"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Height" Value="28"/>
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="SelectionBrush" Value="#A83232"/>
			<Setter Property="SelectionForegroundBrush" Value="White"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.2"/>
					<BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.1"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="TextBox:focus /template/ Border#PART_BorderElement">
			<Setter Property="BorderBrush" Value="#A83232"/>
			<Setter Property="BorderThickness" Value="1"/>
		</Style>

		<Style Selector="TextBox:pointerover">
			<Setter Property="Background" Value="#2A2A2A"/>
			<Setter Property="RenderTransform" Value="scale(1.02)"/>
		</Style>

		<Style Selector="TextBox:focus">
			<Setter Property="Background" Value="#2D2D2D"/>
			<Setter Property="RenderTransform" Value="scale(1.02)"/>
		</Style>

		<Style Selector="ComboBox">
			<Setter Property="Background" Value="#222222"/>
			<Setter Property="Foreground" Value="#E8E8E8"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Padding" Value="6,4"/>
			<Setter Property="Height" Value="28"/>
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="HorizontalAlignment" Value="Stretch"/>
		</Style>

		<Style Selector="ComboBox:pointerover /template/ Border#Background">
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>

		<!-- Enhanced CheckBox with Animation -->
		<Style Selector="CheckBox">
			<Setter Property="Foreground" Value="#E8E8E8"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="VerticalAlignment" Value="Center"/>
			<Setter Property="FontSize" Value="12"/>
			<Setter Property="Margin" Value="0,2"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Foreground" Duration="0:0:0.2"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="CheckBox:pointerover">
			<Setter Property="Foreground" Value="#FFFFFF"/>
			<Setter Property="RenderTransform" Value="scale(1.05)"/>
		</Style>

		<Style Selector="CheckBox:checked /template/ Border#NormalRectangle">
			<Setter Property="Background" Value="#A83232"/>
			<Setter Property="BorderBrush" Value="#A83232"/>
		</Style>

		<Style Selector="CheckBox:checked">
			<Setter Property="Foreground" Value="#4CAF50"/>
		</Style>

		<!-- Enhanced ProgressBar with Animation -->
		<Style Selector="ProgressBar">
			<Setter Property="Foreground" Value="#A83232"/>
			<Setter Property="Background" Value="#303030"/>
			<Setter Property="CornerRadius" Value="2"/>
			<Setter Property="Height" Value="4"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="scaleX(0.8)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.3"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.3"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="ProgressBar.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="scaleX(1.0)"/>
		</Style>

		<!-- Enhanced Right Panel with Animation -->
		<Style Selector="Border#RightPanelBorder">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(50px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.8" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.8" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border#RightPanelBorder.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<Style Selector="SelectableTextBlock.log-area">
			<Setter Property="Background" Value="#1A1A1A"/>
			<Setter Property="Foreground" Value="#EFEFEF"/>
			<Setter Property="FontFamily" Value="Consolas, Courier New, monospace"/>
			<Setter Property="FontSize" Value="11"/>
			<Setter Property="TextWrapping" Value="Wrap"/>
			<Setter Property="Padding" Value="8"/>
			<Setter Property="LineHeight" Value="16"/>
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(10px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="SelectableTextBlock.log-area.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
		</Style>

		<Style Selector="ScrollViewer#LogScroll">
			<Setter Property="Background" Value="#1A1A1A"/>
			<Setter Property="BorderBrush" Value="#3F3F3F"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
		</Style>

		<Style Selector="TextBlock.field-label">
			<Setter Property="Foreground" Value="#B8B8B8"/>
			<Setter Property="FontWeight" Value="Normal"/>
			<Setter Property="FontSize" Value="12"/>
		</Style>

		<!-- Enhanced License Status Bar with Animation -->
		<Style Selector="Border#LicenseStatusBar">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(20px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.6" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.6" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border#LicenseStatusBar.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
		</Style>

		<!-- Enhanced Status Bar with Animation -->
		<Style Selector="Border#StatusBarBorder">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateY(15px)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.5" Easing="CubicEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.5" Easing="CubicEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Border#StatusBarBorder.loaded">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateY(0px)"/>
		</Style>

		<!-- Ultra Smooth Tab Transition Animations -->

		<!-- Exit Animation: Thu hồi tab cũ với hiệu ứng mượt -->
		<Style Selector="Grid.content-exit">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-50px) scale(0.9)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.16" Easing="ExponentialEaseIn"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.16" Easing="ExponentialEaseIn"/>
				</Transitions>
			</Setter>
		</Style>

		<!-- Enter Animation: Hiện tab mới với hiệu ứng bounce nhẹ -->
		<Style Selector="Grid.content-enter">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px) scale(1)"/>
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.22" Easing="BackEaseOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.22" Easing="BackEaseOut"/>
				</Transitions>
			</Setter>
		</Style>

		<!-- Show State: Trạng thái hiển thị bình thường -->
		<Style Selector="Grid.content-show">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px) scale(1)"/>
		</Style>

		<!-- Default State: Trạng thái ban đầu -->
		<Style Selector="Grid[Name=functionsContent], Grid[Name=odinContent], Grid[Name=advancedContent]">
			<Setter Property="RenderTransform" Value="translateX(40px) scale(0.95)"/>
			<Setter Property="Opacity" Value="0"/>
		</Style>

		<!-- Enhanced Tab Item Interactions -->
		<Style Selector="ListBoxItem">
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Background" Duration="0:0:0.12"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.12"/>
					<DoubleTransition Property="Opacity" Duration="0:0:0.12"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="ListBoxItem:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.02)"/>
		</Style>

		<Style Selector="ListBoxItem:pressed">
			<Setter Property="RenderTransform" Value="scale(0.98)"/>
		</Style>

		<!-- Smooth Content Area Transitions -->
		<Style Selector="Panel[Name=contentArea]">
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.1"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="PathIcon">
			<Setter Property="Foreground" Value="#E8E8E8"/>
			<Setter Property="Transitions">
				<Transitions>
					<BrushTransition Property="Foreground" Duration="0:0:0.3"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="PathIcon:pointerover">
			<Setter Property="RenderTransform" Value="scale(1.1)"/>
		</Style>

		<!-- Enhanced Content Switching Animation -->
		<Style Selector="Grid#FunctionsGrid, Grid#OdinGrid, Grid#AdvancedGrid">
			<Setter Property="Transitions">
				<Transitions>
					<DoubleTransition Property="Opacity" Duration="0:0:0.3" Easing="CubicEaseInOut"/>
					<TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.3" Easing="CubicEaseInOut"/>
				</Transitions>
			</Setter>
		</Style>

		<Style Selector="Grid#FunctionsGrid.show, Grid#OdinGrid.show, Grid#AdvancedGrid.show">
			<Setter Property="Opacity" Value="1"/>
			<Setter Property="RenderTransform" Value="translateX(0px)"/>
		</Style>

		<Style Selector="Grid#FunctionsGrid.hide, Grid#OdinGrid.hide, Grid#AdvancedGrid.hide">
			<Setter Property="Opacity" Value="0"/>
			<Setter Property="RenderTransform" Value="translateX(-30px)"/>
		</Style>
	</Window.Styles>

	<Grid RowDefinitions="*,Auto" ColumnDefinitions="200,*,480" Name="MainGrid">
		<Border Grid.Row="0" Grid.Column="0" Classes="sidebar" Name="SidebarBorder">
			<Grid RowDefinitions="Auto,*">
				<StackPanel Grid.Row="0" Margin="12,16,12,16" Spacing="12" Name="LogoPanel">
					<TextBlock Text="SAMSUNG TOOL" Classes="logo" HorizontalAlignment="Center" Name="LogoText"/>
					<Border Classes="separator"/>
				</StackPanel>

				<ListBox Grid.Row="1"
                         x:Name="tabListBox"
                         Classes="sidebar-tabs"
                         SelectedIndex="0"
                         Margin="8,0"
                         Name="TabListBox">
					<ListBoxItem x:Name="functionsTab">
						<StackPanel Orientation="Horizontal" Spacing="10">
							<PathIcon Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" Width="16" Height="16"/>
							<TextBlock Text="FUNCTIONS" VerticalAlignment="Center" FontSize="12"/>
						</StackPanel>
					</ListBoxItem>
					<ListBoxItem x:Name="odinTab">
						<StackPanel Orientation="Horizontal" Spacing="10">
							<PathIcon Data="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" Width="16" Height="16"/>
							<TextBlock Text="ODIN MODE" VerticalAlignment="Center" FontSize="12"/>
						</StackPanel>
					</ListBoxItem>
					<ListBoxItem x:Name="advancedTab">
						<StackPanel Orientation="Horizontal" Spacing="10">
							<PathIcon Data="M21.71,20.29L20.29,21.71C20.11,21.89 19.85,22 19.59,22C19.33,22 19.07,21.89 18.88,21.71L7,9.85C6.14,10.64 4.9,11.05 3.71,10.92C2.5,10.79 1.47,10.05 0.89,9C0.31,7.9 0.32,6.61 0.88,5.56C1.27,4.81 2.22,3.76 3.87,3.75C4.14,3.75 4.44,3.79 4.73,3.89C4.91,3.95 5.04,4.12 5.05,4.31C5.06,4.5 4.95,4.68 4.78,4.75C3.7,5.22 3.22,6.22 3.37,7.17C3.53,8.11 4.3,8.86 5.23,9C6.16,9.12 7.07,8.68 7.58,7.84C7.61,7.77 7.66,7.72 7.71,7.66L18.87,18.86C19.25,19.26 19.25,19.92 18.87,20.31M17.41,12.13L18.87,10.68C19.06,10.47 19.06,10.14 18.87,9.94L18.07,9.13C17.88,8.94 17.55,8.94 17.34,9.13L15.89,10.58L17.41,12.13M17.88,14.63L19.34,13.19C19.52,12.98 19.52,12.66 19.34,12.46L18.54,11.65C18.35,11.46 18,11.46 17.81,11.65L16.36,13.1L17.88,14.63M14.37,7.35L15.83,5.92C16,5.73 16,5.4 15.83,5.21L15.03,4.4C14.84,4.21 14.5,4.21 14.31,4.4L12.88,5.84L14.37,7.35M10.86,3.86L12.31,2.42C12.5,2.22 12.5,1.89 12.31,1.7L11.5,0.89C11.31,0.7 11,0.7 10.77,0.89L9.34,2.33L10.86,3.86Z" Width="16" Height="16"/>
							<TextBlock Text="FLASH FIRMWARE" VerticalAlignment="Center" FontSize="12"/>
						</StackPanel>
					</ListBoxItem>
				</ListBox>
			</Grid>
		</Border>

		<Grid Grid.Row="0" Grid.Column="1" RowDefinitions="Auto,*,Auto" Name="ContentGrid">
			<Border Grid.Row="0" Background="#1E1E1E"
                    BorderBrush="#3F3F3F" BorderThickness="0,0,0,1"
                    Padding="16,12" Name="ContentTitleBorder">
				<TextBlock x:Name="contentTitle"
                           Text="FUNCTIONS"
                           Classes="content-title"
                           VerticalAlignment="Center"/>
			</Border>

			<ScrollViewer Grid.Row="1" Padding="0,0,8,0" Name="ContentScrollViewer">
				<Panel x:Name="contentArea" Margin="16,12">
					<Grid x:Name="functionsContent" RowDefinitions="Auto,Auto" RowSpacing="12" IsVisible="True" Name="FunctionsGrid"
					      Classes="content-show">
						<Border Grid.Row="0" Classes="card" Name="MtpCard">
							<StackPanel Spacing="8">
								<TextBlock Text="MTP OPERATIONS" Classes="group-header" Name="MtpHeader"/>
								<Border Classes="separator"/>

								<UniformGrid Columns="2" Rows="3" RowSpacing="4" ColumnSpacing="8">
									<Button x:Name="btnReadinfoMTP"
                                            Content="Reading Information [ MTP ]"
                                            Classes="function"/>
									<Button x:Name="btnRebootdownloadmtp"
                                            Content="Reboot to Download Mode [ MTP ]"
                                            Classes="function"/>
									<Button x:Name="btnQROS13"
                                            Content="Factory Reset [ MTP ]"
                                            Classes="function"/>
									<Button x:Name="btnRebootNormalMTP"
                                            Content="Reboot Normal [ MTP ]"
                                            Classes="function"/>
									<Button x:Name="btnQROS14"
                                            Content="QR CODE [ MTP ]"
                                            Classes="function"/>
									<Button x:Name="btnChangeColorMTP"
                                            Content="Change Color Code [ MTP ]"
                                            Classes="function"/>
								</UniformGrid>
							</StackPanel>
						</Border>

						<Border Grid.Row="1" Classes="card" Name="AdbCard">
							<StackPanel Spacing="8">
								<TextBlock Text="ADB OPERATIONS" Classes="group-header" Name="AdbHeader"/>
								<Border Classes="separator"/>

								<UniformGrid Columns="2" Rows="5" RowSpacing="4" ColumnSpacing="8">
									<Button x:Name="btnReadInfoADB"
									        Content="Reading Information [ ADB ]"
										    Classes="function"/>
									<Button x:Name="btnRemoveFRPADB"
											Content="Remove FRP [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnRebootDownloadADB"
											Content="Reboot to Download Mode [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnRebootrecovery"
											Content="Reboot to Recovery [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnKGOS14"
											Content="KG Bypass [10 - 2024] [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnKGApps"
											Content="KG Bypass 2025 [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnKGRemove2025"
											Content="KG Remove ALL OS 14 [ 06 - 2025 ] [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnChangeCSC"
											Content="Change CSC [ 06 - 2025 ] [ ADB ]"
											Classes="function"/>
									<Button x:Name="btnChangeSerial"
											Content="Change Serial [ 06 - 2025 ] [ ADB ]"
											Classes="function"/>
									
									<Border/>
								</UniformGrid>
							</StackPanel>
						</Border>
					</Grid>

					<Grid x:Name="odinContent" IsVisible="False" Name="OdinGrid">
						<Border Classes="card" Name="OdinCard">
							<StackPanel Spacing="8">
								<TextBlock Text="DOWNLOAD MODE OPERATIONS" Classes="group-header" Name="OdinHeader"/>
								<Border Classes="separator"/>

								<UniformGrid Columns="2" Rows="2" RowSpacing="4" ColumnSpacing="8" Name="OdinButtonGrid">
									<Button x:Name="btnReadinfo"
                                            Content="Reading Information"
                                            Classes="function"/>
									<Button x:Name="btnFixSoftBrick"
                                            Content="Fix SoftBrick"
                                            Classes="function"/>
									<Button x:Name="btnFactoryReset"
                                            Content="Factory Reset"
                                            Classes="function"/>
									<Button x:Name="btnRemoveFRPMTK"
                                            Content="Remove FRP MTK"
                                            Classes="function"/>
								</UniformGrid>
							</StackPanel>
						</Border>
					</Grid>

					<Grid x:Name="advancedContent" RowDefinitions="Auto,Auto,Auto" RowSpacing="12" IsVisible="False" Name="AdvancedGrid">
						<Border Grid.Row="0" Classes="card" Name="FirmwareCard">
							<StackPanel Spacing="8">
								<TextBlock Text="FIRMWARE FILES" Classes="group-header" Name="FirmwareHeader"/>
								<Border Classes="separator"/>

								<Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto,Auto,Auto" RowSpacing="6">
									<CheckBox x:Name="cbFlashBL" Grid.Row="0" Grid.Column="0" Content="BL" VerticalAlignment="Center"/>
									<TextBox x:Name="txtFlashBL" Grid.Row="0" Grid.Column="1" Margin="8,0" Watermark="Select BL file..."/>
									<Button x:Name="btnBL" Grid.Row="0" Grid.Column="2" Content="..." Classes="file-browse"/>

									<CheckBox x:Name="cbFlashAP" Grid.Row="1" Grid.Column="0" Content="AP" VerticalAlignment="Center"/>
									<TextBox x:Name="txtFlashAP" Grid.Row="1" Grid.Column="1" Margin="8,0" Watermark="Select AP file..."/>
									<Button x:Name="btnAP" Grid.Row="1" Grid.Column="2" Content="..." Classes="file-browse"/>

									<CheckBox x:Name="cbFlashCP" Grid.Row="2" Grid.Column="0" Content="CP" VerticalAlignment="Center"/>
									<TextBox x:Name="txtFlashCP" Grid.Row="2" Grid.Column="1" Margin="8,0" Watermark="Select CP file..."/>
									<Button x:Name="btnCP" Grid.Row="2" Grid.Column="2" Content="..." Classes="file-browse"/>

									<CheckBox x:Name="cbFlashCSC" Grid.Row="3" Grid.Column="0" Content="CSC" VerticalAlignment="Center"/>
									<TextBox x:Name="txtFlashCSC" Grid.Row="3" Grid.Column="1" Margin="8,0" Watermark="Select CSC file..."/>
									<Button x:Name="btnCSC" Grid.Row="3" Grid.Column="2" Content="..." Classes="file-browse"/>

									<CheckBox x:Name="cbFlashDATA" Grid.Row="4" Grid.Column="0" Content="DATA" VerticalAlignment="Center"/>
									<TextBox x:Name="txtFlashDATA" Grid.Row="4" Grid.Column="1" Margin="8,0" Watermark="Select DATA file..."/>
									<Button x:Name="btnHome" Grid.Row="4" Grid.Column="2" Content="..." Classes="file-browse"/>
								</Grid>
							</StackPanel>
						</Border>

						<Border Grid.Row="1" Classes="card" Name="FlashOptionsCard">
							<StackPanel Spacing="8">
								<TextBlock Text="FLASH OPTIONS" Classes="group-header" Name="FlashOptionsHeader"/>
								<Border Classes="separator"/>

								<StackPanel Orientation="Horizontal" Spacing="16">
									<CheckBox x:Name="CkMD5" Content="Validate MD5"/>
									<CheckBox x:Name="ckAutoReboot" Content="Auto Reboot" IsChecked="True"/>
									<CheckBox x:Name="CkNandErase" Content="Nand Erase"/>
								</StackPanel>
							</StackPanel>
						</Border>

						<Border Grid.Row="2" Classes="card" Name="FlashActionsCard">
							<StackPanel Spacing="8">
								<TextBlock Text="FLASH ACTIONS" Classes="group-header" Name="FlashActionsHeader"/>
								<Border Classes="separator"/>

								<Grid ColumnDefinitions="*,*" ColumnSpacing="8">
									<Button x:Name="btnClearFirmware"
                                            Content="Clear Firmware"
                                            Grid.Column="0"/>
									<Button x:Name="btnFlash"
                                            Content="Flash Firmware"
                                            Grid.Column="1"
                                            Classes="accent"/>
								</Grid>
							</StackPanel>
						</Border>
					</Grid>
				</Panel>
			</ScrollViewer>

			<Border Grid.Row="2" Background="#1E1E1E"
                    BorderBrush="#3F3F3F" BorderThickness="0,1,0,0"
                    Padding="16,8" Name="StatusBarBorder">
				<Grid RowDefinitions="Auto,Auto,Auto" RowSpacing="4">
					<Grid Grid.Row="0" ColumnDefinitions="*,Auto">
						<StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
							<TextBlock x:Name="operationStatus" Text="Ready"
                                       Foreground="#A83232" FontWeight="Medium"
                                       FontSize="12" VerticalAlignment="Center"/>
							<TextBlock x:Name="processName" Text=""
                                       Margin="8,0,0,0" FontSize="11"
                                       VerticalAlignment="Center"
                                       TextWrapping="Wrap"
                                       MaxWidth="280"/>
						</StackPanel>

						<Button Grid.Column="1" x:Name="btnCancelOperation"
                                Content="Cancel Operation"
                                IsEnabled="False"
                                Classes="cancel-button"
                                Width="150"/>
					</Grid>

					<ProgressBar Grid.Row="1" x:Name="operationProgress" IsVisible="False"/>
					<ProgressBar Grid.Row="2" x:Name="Loading" IsVisible="False"/>
				</Grid>
			</Border>
		</Grid>

		<Border Grid.Row="0" Grid.Column="2"
                Background="#252525"
                BorderBrush="#3F3F3F"
                BorderThickness="1,0,0,0"
                Name="RightPanelBorder">
			<Grid RowDefinitions="Auto,*">
				<Border Grid.Row="0" Classes="card" Margin="12,16,12,12" Name="DeviceConnectionCard">
					<StackPanel Spacing="8">
						<TextBlock Text="DEVICE CONNECTION" Classes="group-header" Name="DeviceConnectionHeader"/>
						<Border Classes="separator"/>

						<Grid ColumnDefinitions="Auto,*" RowSpacing="4">
							<TextBlock Text="Port:" Grid.Column="0" VerticalAlignment="Center"
                                       Margin="0,0,8,0" Classes="field-label"/>
							<ComboBox x:Name="cmbCOM" Grid.Column="1"/>
						</Grid>
					</StackPanel>
				</Border>

				<Grid Grid.Row="1" RowDefinitions="Auto,*" Margin="12,0,12,12" Name="LogsGrid">
					<Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto" Margin="0,0,0,8">
						<TextBlock Text="LOGS" Classes="group-header" Name="LogsHeader"/>
						<Button x:Name="btnHistory" Grid.Column="1" Content="History" Classes="compact" Margin="0,0,8,0"/>
						<Button x:Name="btnClearLogs" Grid.Column="2" Content="Clear" Classes="compact"/>
					</Grid>

					<ScrollViewer Grid.Row="1" x:Name="LogScroll" VerticalScrollBarVisibility="Auto" Name="LogScrollViewer">
						<SelectableTextBlock x:Name="Richlogs" Classes="log-area">
							<SelectableTextBlock.Inlines/>
						</SelectableTextBlock>
					</ScrollViewer>
				</Grid>
			</Grid>
		</Border>

		<!-- Enhanced License Status Bar with Animation -->
		<Border Grid.Row="1" Grid.ColumnSpan="3"
		        Background="#2B2B2B"
		        BorderBrush="#3F3F3F"
		        BorderThickness="0,1,0,0"
		        Padding="16,8"
		        Name="LicenseStatusBar">
			<Grid ColumnDefinitions="Auto,*,Auto">
				<StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8" Name="LicenseStatusPanel">
					<PathIcon x:Name="licenseStatusIcon"
					          Data="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10.1V11.1C15.3,11.1 15.7,11.5 15.7,12V16C15.7,16.4 15.3,16.8 14.8,16.8H9.2C8.7,16.8 8.3,16.4 8.3,16V12C8.3,11.5 8.7,11.1 9.2,11.1V10.1C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10.1V11.1H13.6V10.1C13.6,8.7 12.8,8.2 12,8.2Z"
					          Width="16" Height="16"
					          Foreground="#4CAF50"
					          VerticalAlignment="Center"/>
					<TextBlock Text="License Status:"
					           Foreground="#B8B8B8"
					           FontSize="12"
					           VerticalAlignment="Center"/>
					<Ellipse x:Name="licenseStatusIndicator"
					         Width="8" Height="8"
					         Fill="#4CAF50"
					         VerticalAlignment="Center"/>
					<TextBlock x:Name="licenseStatus"
					           Text="Active"
					           Foreground="#4CAF50"
					           FontSize="12"
					           FontWeight="Medium"
					           VerticalAlignment="Center"/>
				</StackPanel>

				<Border Grid.Column="1"/>

				<StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="16" Name="LicenseInfoPanel">
					<StackPanel Orientation="Horizontal" Spacing="4">
						<TextBlock Text="User:"
						           Foreground="#B8B8B8"
						           FontSize="12"
						           VerticalAlignment="Center"/>
						<TextBlock x:Name="licenseUsername"
						           Text="Loading..."
						           Foreground="#E8E8E8"
						           FontWeight="Medium"
						           FontSize="12"
						           VerticalAlignment="Center"/>
					</StackPanel>

					<Border Background="#3F3F3F" Width="1" Height="16" VerticalAlignment="Center"/>

					<StackPanel Orientation="Horizontal" Spacing="4">
						<TextBlock Text="Expired:"
						           Foreground="#B8B8B8"
						           FontSize="12"
						           VerticalAlignment="Center"/>
						<TextBlock x:Name="licenseExpiration"
						           Text="Loading..."
						           Foreground="#4CAF50"
						           FontWeight="Medium"
						           FontSize="12"
						           VerticalAlignment="Center"/>
					</StackPanel>
				</StackPanel>
			</Grid>
		</Border>
	</Grid>
</Window>