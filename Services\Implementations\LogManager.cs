using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using SamsungTool.Models;
using SamsungTool.Services.Interfaces;
using SamsungTool.Library;

namespace SamsungTool.Services.Implementations
{




    /// <summary>
    /// Implementation of log management service
    /// </summary>
    public class LogManager : ILogManager, IDisposable
    {
        private readonly ConcurrentDictionary<string, LogEntry> _activeSessions = new();
        private readonly string _logsDirectory;
        private readonly string _historyFilePath;
        private LogEntryCollection _logHistory;
        private readonly object _lockObject = new();

        // Background processing queue for file operations
        private readonly ConcurrentQueue<Func<Task>> _backgroundTasks = new();
        private readonly SemaphoreSlim _backgroundSemaphore = new(1, 1);
        private readonly CancellationTokenSource _cancellationTokenSource = new();

        // Cache for recently loaded log content
        private readonly ConcurrentDictionary<string, (string content, DateTime loadTime)> _contentCache = new();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        public string? CurrentLogSessionId { get; private set; }

        public event EventHandler<LogEntry>? LogSessionCompleted;

        public LogManager()
        {
            // Create logs directory in the same folder as executable
            var exeDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _logsDirectory = Path.Combine(exeDirectory, "Logs");
            _historyFilePath = Path.Combine(_logsDirectory, "history.json");

            _logHistory = new LogEntryCollection();

            // Ensure logs directory exists
            Directory.CreateDirectory(_logsDirectory);

            // Load existing history in background
            _ = Task.Run(LoadHistoryAsync);

            // Start background task processor
            _ = Task.Run(ProcessBackgroundTasks);
        }

        /// <summary>
        /// Background task processor for file operations
        /// </summary>
        private async Task ProcessBackgroundTasks()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_backgroundTasks.TryDequeue(out var task))
                    {
                        await _backgroundSemaphore.WaitAsync(_cancellationTokenSource.Token);
                        try
                        {
                            await task();
                        }
                        finally
                        {
                            _backgroundSemaphore.Release();
                        }
                    }
                    else
                    {
                        await Task.Delay(100, _cancellationTokenSource.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    ErrorLogger.LogError("LogManager.ProcessBackgroundTasks", ex);
                }
            }
        }

        public string StartLogSession(string operationName, string operationType)
        {
            if (string.IsNullOrEmpty(operationName))
                operationName = "Unknown Operation";
            if (string.IsNullOrEmpty(operationType))
                operationType = "Unknown";

            var logEntry = new LogEntry
            {
                StartTime = DateTime.Now,
                OperationName = operationName,
                OperationType = operationType,
                Status = LogEntryStatus.InProgress
            };

            _activeSessions[logEntry.Id] = logEntry;
            CurrentLogSessionId = logEntry.Id;

            return logEntry.Id;
        }

        public async Task<string> EndLogSessionAsync(string logId, LogEntryStatus status, string logContent)
        {
            if (string.IsNullOrEmpty(logId) || !_activeSessions.TryRemove(logId, out var logEntry))
                return string.Empty;

            logEntry.EndTime = DateTime.Now;
            logEntry.Status = status;
            logEntry.LogContent = logContent;
            logEntry.PlainTextContent = StripRichTextFormatting(logContent);

            if (CurrentLogSessionId == logId)
                CurrentLogSessionId = null;

            // Generate filename based on operation and timestamp
            var timestamp = logEntry.StartTime.ToString("yyyyMMdd_HHmmss");
            var safeOperationName = MakeSafeFileName(logEntry.OperationName);
            var fileName = $"logsession_{safeOperationName}_{timestamp}.txt";
            var filePath = Path.Combine(_logsDirectory, fileName);

            // Queue file operations for background processing
            var tcs = new TaskCompletionSource<string>();
            _backgroundTasks.Enqueue(async () =>
            {
                try
                {
                    // Save log content to file
                    await SaveLogToFileAsync(filePath, logEntry);
                    logEntry.FilePath = filePath;

                    // Add to history
                    lock (_lockObject)
                    {
                        _logHistory.AddEntry(logEntry);
                    }

                    // Save history
                    await SaveHistoryAsync();

                    // Fire event on UI thread
                    _ = Task.Run(() => LogSessionCompleted?.Invoke(this, logEntry));

                    tcs.SetResult(filePath);
                }
                catch (Exception ex)
                {
                    // If file save fails, still add to history but without file path
                    logEntry.FilePath = $"Error saving: {ex.Message}";

                    lock (_lockObject)
                    {
                        _logHistory.AddEntry(logEntry);
                    }

                    ErrorLogger.LogError("LogManager.EndLogSessionAsync", ex);
                    tcs.SetResult(string.Empty);
                }
            });

            return await tcs.Task;
        }

        public void UpdateLogContent(string logId, string additionalContent)
        {
            if (string.IsNullOrEmpty(logId))
                return;

            lock (_lockObject)
            {
                if (_activeSessions.TryGetValue(logId, out var logEntry))
                {
                    logEntry.LogContent += additionalContent ?? string.Empty;
                    logEntry.PlainTextContent = StripRichTextFormatting(logEntry.LogContent);
                }
            }
        }

        public async Task<LogEntryCollection> GetLogHistoryAsync()
        {
            await LoadHistoryAsync();
            
            lock (_lockObject)
            {
                // Return a copy to avoid concurrent modification
                var copy = new LogEntryCollection();
                copy.Entries.AddRange(_logHistory.Entries);
                return copy;
            }
        }

        public async Task<LogEntry?> GetLogEntryAsync(string logId)
        {
            await LoadHistoryAsync();
            
            lock (_lockObject)
            {
                return _logHistory.Entries.Find(e => e.Id == logId);
            }
        }

        public async Task<bool> DeleteLogEntryAsync(string logId)
        {
            try
            {
                await LoadHistoryAsync();
                
                LogEntry? entryToDelete;
                lock (_lockObject)
                {
                    entryToDelete = _logHistory.Entries.Find(e => e.Id == logId);
                    if (entryToDelete == null)
                        return false;

                    _logHistory.Entries.Remove(entryToDelete);
                }

                // Delete associated file if it exists
                if (!string.IsNullOrEmpty(entryToDelete.FilePath) && File.Exists(entryToDelete.FilePath))
                {
                    File.Delete(entryToDelete.FilePath);
                }

                await SaveHistoryAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearAllHistoryAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    // Delete all log files
                    foreach (var entry in _logHistory.Entries)
                    {
                        if (!string.IsNullOrEmpty(entry.FilePath) && File.Exists(entry.FilePath))
                        {
                            try { File.Delete(entry.FilePath); } catch { }
                        }
                    }

                    _logHistory.Entries.Clear();
                }

                await SaveHistoryAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> ReadLogFileAsync(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return "Log file not found.";

                // Check cache first
                if (_contentCache.TryGetValue(filePath, out var cached))
                {
                    // Check if cache is still valid
                    if (DateTime.Now - cached.loadTime < _cacheExpiry)
                    {
                        return cached.content;
                    }
                    else
                    {
                        // Remove expired cache entry
                        _contentCache.TryRemove(filePath, out _);
                    }
                }

                // Read from file and cache the result
                var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                _contentCache[filePath] = (content, DateTime.Now);

                // Clean up old cache entries periodically
                if (_contentCache.Count > 50)
                {
                    CleanupCache();
                }

                return content;
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError("LogManager.ReadLogFileAsync", ex);
                return $"Error reading log file: {ex.Message}";
            }
        }

        /// <summary>
        /// Clean up expired cache entries
        /// </summary>
        private void CleanupCache()
        {
            var now = DateTime.Now;
            var expiredKeys = new List<string>();

            foreach (var kvp in _contentCache)
            {
                if (now - kvp.Value.loadTime >= _cacheExpiry)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _contentCache.TryRemove(key, out _);
            }
        }

        public async Task<bool> ExportHistoryAsync(string exportPath)
        {
            try
            {
                await LoadHistoryAsync();

                var exportLines = new List<string>();
                exportLines.Add($"Samsung Tool - Log History Export");
                exportLines.Add($"Export Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                exportLines.Add($"Total Entries: {_logHistory.Entries.Count}");
                exportLines.Add("=".PadRight(80, '='));
                exportLines.Add("");

                foreach (var entry in _logHistory.Entries)
                {
                    exportLines.Add($"Operation: {entry.OperationName}");
                    exportLines.Add($"Start Time: {entry.StartTimeString}");
                    exportLines.Add($"End Time: {entry.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}");
                    exportLines.Add($"Duration: {entry.DurationString}");
                    exportLines.Add($"Status: {entry.StatusDisplay}");
                    exportLines.Add($"File: {entry.FilePath}");
                    exportLines.Add("-".PadRight(80, '-'));
                    exportLines.Add("");
                }

                await File.WriteAllLinesAsync(exportPath, exportLines, Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task LoadHistoryAsync()
        {
            try
            {
                if (!File.Exists(_historyFilePath))
                    return;

                var lines = await File.ReadAllLinesAsync(_historyFilePath, Encoding.UTF8);
                var loadedEntries = new List<LogEntry>();

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 7)
                    {
                        var entry = new LogEntry
                        {
                            Id = parts[0],
                            OperationName = parts[1],
                            OperationType = parts[2],
                            StartTime = DateTime.TryParse(parts[3], out var startTime) ? startTime : DateTime.Now,
                            EndTime = DateTime.TryParse(parts[4], out var endTime) ? endTime : null,
                            Status = Enum.TryParse<LogEntryStatus>(parts[5], out var status) ? status : LogEntryStatus.Success,
                            FilePath = parts[6]
                        };

                        // Load log content from file if it exists
                        if (!string.IsNullOrEmpty(entry.FilePath) && File.Exists(entry.FilePath))
                        {
                            try
                            {
                                var fileContent = await File.ReadAllTextAsync(entry.FilePath, Encoding.UTF8);
                                entry.PlainTextContent = fileContent;
                                entry.LogContent = fileContent;
                            }
                            catch
                            {
                                // If file can't be read, leave content empty
                            }
                        }

                        loadedEntries.Add(entry);
                    }
                }

                lock (_lockObject)
                {
                    _logHistory.Entries.Clear();
                    _logHistory.Entries.AddRange(loadedEntries);
                }
            }
            catch
            {
                // If loading fails, start with empty history
                lock (_lockObject)
                {
                    _logHistory = new LogEntryCollection();
                }
            }
        }

        private async Task SaveHistoryAsync()
        {
            try
            {
                var historyLines = new List<string>();

                lock (_lockObject)
                {
                    foreach (var entry in _logHistory.Entries)
                    {
                        // Simple format: ID|OperationName|OperationType|StartTime|EndTime|Status|FilePath
                        var line = $"{entry.Id}|{entry.OperationName}|{entry.OperationType}|{entry.StartTime:yyyy-MM-dd HH:mm:ss}|{entry.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""}|{entry.Status}|{entry.FilePath}";
                        historyLines.Add(line);
                    }
                }

                await File.WriteAllLinesAsync(_historyFilePath, historyLines, Encoding.UTF8);
            }
            catch
            {
                // Ignore save errors to prevent application crashes
            }
        }

        private async Task SaveLogToFileAsync(string filePath, LogEntry logEntry)
        {
            var content = new StringBuilder();
            content.AppendLine("=".PadRight(80, '='));
            content.AppendLine($"Samsung Tool - Log Session");
            content.AppendLine("=".PadRight(80, '='));
            content.AppendLine($"Operation: {logEntry.OperationName}");
            content.AppendLine($"Start Time: {logEntry.StartTimeString}");
            content.AppendLine($"End Time: {logEntry.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "N/A"}");
            content.AppendLine($"Duration: {logEntry.DurationString}");
            content.AppendLine($"Status: {logEntry.StatusDisplay}");
            content.AppendLine("=".PadRight(80, '='));
            content.AppendLine();
            content.AppendLine("LOG CONTENT:");
            content.AppendLine("-".PadRight(80, '-'));
            content.AppendLine(logEntry.PlainTextContent);
            content.AppendLine();
            content.AppendLine("=".PadRight(80, '='));
            content.AppendLine($"Log saved at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            await File.WriteAllTextAsync(filePath, content.ToString(), Encoding.UTF8);
        }

        private static string StripRichTextFormatting(string richText)
        {
            if (string.IsNullOrEmpty(richText))
                return string.Empty;

            // Remove common rich text formatting patterns
            var plainText = richText;
            
            // Remove ANSI escape sequences
            plainText = Regex.Replace(plainText, @"\x1B\[[0-9;]*[mK]", "");
            
            // Remove other control characters except newlines and tabs
            plainText = Regex.Replace(plainText, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");
            
            return plainText.Trim();
        }

        private static string MakeSafeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "unknown";

            // Replace invalid characters with underscores
            var invalidChars = Path.GetInvalidFileNameChars();
            var safeFileName = fileName;
            
            foreach (var invalidChar in invalidChars)
            {
                safeFileName = safeFileName.Replace(invalidChar, '_');
            }

            // Replace spaces with underscores and limit length
            safeFileName = safeFileName.Replace(' ', '_');
            
            if (safeFileName.Length > 50)
                safeFileName = safeFileName.Substring(0, 50);

            return safeFileName.ToLowerInvariant();
        }

        public void Dispose()
        {
            try
            {
                _cancellationTokenSource.Cancel();
                _backgroundSemaphore?.Dispose();
                _cancellationTokenSource?.Dispose();
                _contentCache.Clear();
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError("LogManager.Dispose", ex);
            }
        }
    }
}
