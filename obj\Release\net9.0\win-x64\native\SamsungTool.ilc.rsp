--reflectiondata
--stack-trace-data
--methodbodyfolding
obj\Release\net9.0\win-x64\SamsungTool.dll
-o:obj\Release\net9.0\win-x64\native\SamsungTool.obj
-r:C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\9.0.6\runtimes\win-x64\lib\net9.0\WindowsBase.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Base.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Controls.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.DesignerSupport.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Dialogs.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Markup.Xaml.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Markup.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Metal.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.MicroCom.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.OpenGL.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.Vulkan.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia\11.3.0\lib\net8.0\Avalonia.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.desktop\11.3.0\lib\net8.0\Avalonia.Desktop.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.fonts.inter\11.3.0\lib\net8.0\Avalonia.Fonts.Inter.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.freedesktop\11.3.0\lib\net8.0\Avalonia.FreeDesktop.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.native\11.3.0\lib\net8.0\Avalonia.Native.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.3.0\lib\net8.0\Avalonia.Remote.Protocol.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.skia\11.3.0\lib\net8.0\Avalonia.Skia.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.themes.fluent\11.3.0\lib\net8.0\Avalonia.Themes.Fluent.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.win32\11.3.0\lib\net8.0\Avalonia.Win32.Automation.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.win32\11.3.0\lib\net8.0\Avalonia.Win32.dll
-r:C:\Users\<USER>\.nuget\packages\avalonia.x11\11.3.0\lib\net8.0\Avalonia.X11.dll
-r:C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.4.0\lib\net8.0\CommunityToolkit.Mvvm.dll
-r:C:\Users\<USER>\.nuget\packages\harfbuzzsharp\7.3.0.3\lib\net6.0\HarfBuzzSharp.dll
-r:C:\Users\<USER>\.nuget\packages\k4os.compression.lz4\1.2.16\lib\net5.0\K4os.Compression.LZ4.dll
-r:C:\Users\<USER>\.nuget\packages\k4os.compression.lz4.legacy\1.2.16\lib\net5.0\K4os.Compression.LZ4.Legacy.dll
-r:C:\Users\<USER>\.nuget\packages\k4os.compression.lz4.streams\1.2.16\lib\net5.0\K4os.Compression.LZ4.Streams.dll
-r:C:\Users\<USER>\.nuget\packages\k4os.hash.xxhash\1.0.6\lib\netstandard2.0\K4os.Hash.xxHash.dll
-r:C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.5\lib\net9.0\Microsoft.Extensions.DependencyInjection.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.5\lib\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.5\lib\net9.0\Microsoft.Extensions.Logging.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.5\lib\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.5\lib\net9.0\Microsoft.Extensions.Options.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.5\lib\net9.0\Microsoft.Extensions.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\microsoft.win32.systemevents\9.0.5\runtimes\win\lib\net9.0\Microsoft.Win32.SystemEvents.dll
-r:C:\Users\<USER>\.nuget\packages\qrcoder\1.6.0\lib\net6.0\QRCoder.dll
-r:C:\Users\<USER>\.nuget\packages\sharpodinclient\1.0.2\lib\net451\SharpOdinClient.dll
-r:C:\Users\<USER>\.nuget\packages\skiasharp\2.88.9\lib\net6.0\SkiaSharp.dll
-r:C:\Users\<USER>\.nuget\packages\system.codedom\9.0.0\lib\net9.0\System.CodeDom.dll
-r:C:\Users\<USER>\.nuget\packages\system.diagnostics.eventlog\9.0.5\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.Messages.dll
-r:C:\Users\<USER>\.nuget\packages\system.diagnostics.eventlog\9.0.5\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
-r:C:\Users\<USER>\.nuget\packages\system.drawing.common\9.0.5\lib\net9.0\System.Drawing.Common.dll
-r:C:\Users\<USER>\.nuget\packages\system.drawing.common\9.0.5\lib\net9.0\System.Private.Windows.Core.dll
-r:C:\Users\<USER>\.nuget\packages\system.io.ports\9.0.5\runtimes\win\lib\net9.0\System.IO.Ports.dll
-r:C:\Users\<USER>\.nuget\packages\system.management\9.0.0\runtimes\win\lib\net9.0\System.Management.dll
-r:C:\Users\<USER>\.nuget\packages\system.serviceprocess.servicecontroller\9.0.5\runtimes\win\lib\net9.0\System.ServiceProcess.ServiceController.dll
-r:C:\Users\<USER>\.nuget\packages\tmds.dbus.protocol\0.21.2\lib\net8.0\Tmds.DBus.Protocol.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\sdk\System.Private.CoreLib.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\sdk\System.Private.DisabledReflection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\sdk\System.Private.Reflection.Execution.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\sdk\System.Private.StackTraceMetadata.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\sdk\System.Private.TypeLoader.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\Microsoft.CSharp.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\Microsoft.VisualBasic.Core.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\Microsoft.VisualBasic.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\Microsoft.Win32.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\Microsoft.Win32.Registry.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\mscorlib.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\netstandard.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.AppContext.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Buffers.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Collections.Concurrent.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Collections.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Collections.Immutable.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Collections.NonGeneric.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Collections.Specialized.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.Annotations.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.DataAnnotations.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.EventBasedAsync.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ComponentModel.TypeConverter.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Configuration.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Console.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Core.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Data.Common.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Data.DataSetExtensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Data.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.Contracts.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.Debug.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.DiagnosticSource.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.FileVersionInfo.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.Process.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.StackTrace.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.TextWriterTraceListener.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.Tools.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.TraceSource.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Diagnostics.Tracing.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Drawing.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Drawing.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Dynamic.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Formats.Asn1.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Formats.Tar.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Globalization.Calendars.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Globalization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Globalization.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Compression.Brotli.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Compression.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Compression.FileSystem.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Compression.ZipFile.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.FileSystem.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.FileSystem.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.FileSystem.DriveInfo.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.FileSystem.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.FileSystem.Watcher.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.IsolatedStorage.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.MemoryMappedFiles.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Pipelines.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Pipes.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.Pipes.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.IO.UnmanagedMemoryStream.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Linq.Expressions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Linq.Parallel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Linq.Queryable.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Memory.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Http.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Http.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.HttpListener.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Mail.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.NameResolution.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.NetworkInformation.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Ping.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Quic.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Requests.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Security.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.ServicePoint.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.Sockets.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.WebClient.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.WebHeaderCollection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.WebProxy.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.WebSockets.Client.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Net.WebSockets.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Numerics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Numerics.Vectors.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ObjectModel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Private.DataContractSerialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Private.Uri.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Private.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Private.Xml.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.DispatchProxy.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Emit.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Emit.ILGeneration.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Emit.Lightweight.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Metadata.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Reflection.TypeExtensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Resources.Reader.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Resources.ResourceManager.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Resources.Writer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.CompilerServices.Unsafe.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.CompilerServices.VisualC.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Handles.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.InteropServices.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.InteropServices.JavaScript.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.InteropServices.RuntimeInformation.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Intrinsics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Loader.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Numerics.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Serialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Serialization.Formatters.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Serialization.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Serialization.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Runtime.Serialization.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.AccessControl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Claims.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.Algorithms.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.Cng.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.Csp.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.Encoding.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.OpenSsl.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.Primitives.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Cryptography.X509Certificates.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Principal.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.Principal.Windows.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Security.SecureString.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ServiceModel.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ServiceProcess.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.Encoding.CodePages.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.Encoding.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.Encoding.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.Encodings.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.Json.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Text.RegularExpressions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Channels.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Overlapped.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Tasks.Dataflow.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Tasks.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Tasks.Extensions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Tasks.Parallel.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Thread.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.ThreadPool.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Threading.Timer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Transactions.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Transactions.Local.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.ValueTuple.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Web.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Web.HttpUtility.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Windows.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.Linq.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.ReaderWriter.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.Serialization.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.XDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.XmlDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.XmlSerializer.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.XPath.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\System.Xml.XPath.XDocument.dll
-r:C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\9.0.6\framework\WindowsBase.dll
--targetos:win
--targetarch:x64
--dehydrate
-O
-g
--exportsfile:obj\Release\net9.0\win-x64\native\SamsungTool.def
--export-dynamic-symbol:DotNetRuntimeDebugHeader,DATA
--initassembly:System.Private.CoreLib
--initassembly:System.Private.StackTraceMetadata
--initassembly:System.Private.TypeLoader
--initassembly:System.Private.Reflection.Execution
--directpinvoke:System.Globalization.Native
--directpinvoke:System.IO.Compression.Native
--directpinvokelist:C:\Users\<USER>\.nuget\packages\microsoft.dotnet.ilcompiler\9.0.6\build\WindowsAPIs.txt
--feature:MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORT=true
--feature:Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability=true
--feature:System.ComponentModel.DefaultValueAttribute.IsSupported=false
--feature:System.ComponentModel.Design.IDesignerHost.IsSupported=false
--feature:System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization=false
--feature:System.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupported=false
--feature:System.Diagnostics.Tracing.EventSource.IsSupported=false
--feature:System.Reflection.Metadata.MetadataUpdater.IsSupported=false
--feature:System.Resources.ResourceManager.AllowCustomResourceTypes=false
--feature:System.Resources.UseSystemResourceKeys=false
--feature:System.Runtime.CompilerServices.RuntimeFeature.IsDynamicCodeSupported=false
--feature:System.Runtime.InteropServices.BuiltInComInterop.IsSupported=true
--feature:System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting=false
--feature:System.Runtime.InteropServices.EnableCppCLIHostActivation=false
--feature:System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop=false
--feature:System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false
--feature:System.StartupHookProvider.IsSupported=false
--feature:System.Text.Encoding.EnableUnsafeUTF7Encoding=false
--feature:System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault=false
--feature:System.Threading.Thread.EnableAutoreleasePool=false
--feature:System.Threading.ThreadPool.UseWindowsThreadPool=true
--feature:System.Linq.Expressions.CanEmitObjectArrayDelegate=false
--runtimeknob:MVVMTOOLKIT_ENABLE_INOTIFYPROPERTYCHANGING_SUPPORT=true
--runtimeknob:Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability=true
--runtimeknob:System.ComponentModel.DefaultValueAttribute.IsSupported=false
--runtimeknob:System.ComponentModel.Design.IDesignerHost.IsSupported=false
--runtimeknob:System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization=false
--runtimeknob:System.ComponentModel.TypeDescriptor.IsComObjectDescriptorSupported=false
--runtimeknob:System.Diagnostics.Tracing.EventSource.IsSupported=false
--runtimeknob:System.Reflection.Metadata.MetadataUpdater.IsSupported=false
--runtimeknob:System.Resources.ResourceManager.AllowCustomResourceTypes=false
--runtimeknob:System.Resources.UseSystemResourceKeys=false
--runtimeknob:System.Runtime.CompilerServices.RuntimeFeature.IsDynamicCodeSupported=false
--runtimeknob:System.Runtime.InteropServices.BuiltInComInterop.IsSupported=true
--runtimeknob:System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting=false
--runtimeknob:System.Runtime.InteropServices.EnableCppCLIHostActivation=false
--runtimeknob:System.Runtime.InteropServices.Marshalling.EnableGeneratedComInterfaceComImportInterop=false
--runtimeknob:System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization=false
--runtimeknob:System.StartupHookProvider.IsSupported=false
--runtimeknob:System.Text.Encoding.EnableUnsafeUTF7Encoding=false
--runtimeknob:System.Text.Json.JsonSerializer.IsReflectionEnabledByDefault=false
--runtimeknob:System.Threading.Thread.EnableAutoreleasePool=false
--runtimeknob:System.Threading.ThreadPool.UseWindowsThreadPool=true
--runtimeknob:System.Linq.Expressions.CanEmitObjectArrayDelegate=false
--runtimeknob:RUNTIME_IDENTIFIER=win-x64
--stacktracedata
--scanreflection
--methodbodyfolding
--instruction-set:x86-x64
--win32resourcemodule:SamsungTool
--nowarn:"1701;1702;IL2121;1701;1702"
--warnaserr:";NU1605;SYSLIB0011"
--singlewarn
--root:System.Reflection
--root:System.IO.Ports
--root:System.Diagnostics.Process
--root:System.Security.Cryptography
--root:System.Management
--nosinglewarnassembly:SamsungTool
--resilient
--generateunmanagedentrypoints:System.Private.CoreLib
--feature:System.Diagnostics.Debugger.IsSupported=false
