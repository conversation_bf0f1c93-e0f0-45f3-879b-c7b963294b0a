using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SamsungTool.Library;

public static class ProcessManager
{
    private static readonly ConcurrentDictionary<int, Process> _runningProcesses = new();
    private static readonly ConcurrentDictionary<string, CancellationTokenSource> _operationTokens = new();
    private static readonly object _lock = new();
    private static readonly TimeSpan _cleanupInterval = TimeSpan.FromSeconds(30);
    private static Timer? _cleanupTimer;
    private static bool _disposed;


    static ProcessManager()
    {
        _cleanupTimer = new Timer(_ => CleanupFinishedProcesses(), null, _cleanupInterval, _cleanupInterval);
    }

    public static async Task<(int exitCode, string stdOut, string stdErr)> RunProcessAsync(
        string fileName,
        string arguments,
        string workingDirectory,
        string operationId,
        CancellationToken cancellationToken)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ProcessManager));
        var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, GetOrCreateOperationCts(operationId).Token);

        var psi = new ProcessStartInfo
        {
            FileName = fileName,
            Arguments = arguments,
            WorkingDirectory = workingDirectory,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = new Process { StartInfo = psi, EnableRaisingEvents = true };

        RegisterProcess(process);

        var stdOutBuffer = new StringWriter();
        var stdErrBuffer = new StringWriter();

        process.OutputDataReceived += (_, e) => { if (e.Data is not null) stdOutBuffer.WriteLine(e.Data); };
        process.ErrorDataReceived += (_, e) => { if (e.Data is not null) stdErrBuffer.WriteLine(e.Data); };

        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        await process.WaitForExitAsync(cts.Token).ConfigureAwait(false);

        var exitCode = process.ExitCode;

        _runningProcesses.TryRemove(process.Id, out _);

        return (exitCode, stdOutBuffer.ToString(), stdErrBuffer.ToString());
    }

    public static CancellationToken GetOperationToken(string operationId)
        => GetOrCreateOperationCts(operationId).Token;

    public static void CancelOperation(string operationId)
    {
        if (_operationTokens.TryRemove(operationId, out var cts))
        {
            cts.Cancel();
            cts.Dispose();
        }
    }

    public static void RegisterProcess(Process process)
    {
        if (process == null)
            return;

        // Only use _runningProcesses dictionary - removed duplicate _processes collection
        _runningProcesses[process.Id] = process;

        // Enable raising events for the Exited event
        process.EnableRaisingEvents = true;

        process.Exited += (_, _) =>
        {
            _runningProcesses.TryRemove(process.Id, out _);
            // Note: Do NOT dispose the process here as it may be disposed by the using statement
            // The process will be disposed by the caller's using statement or cleanup logic
        };
    }

    public static void TerminateAllProcesses()
    {
        lock (_lock)
        {
            foreach (var kv in _runningProcesses.ToList())
            {
                try
                {
                    // Check if process is still accessible before attempting operations
                    bool hasExited = false;
                    try
                    {
                        hasExited = kv.Value.HasExited;
                    }
                    catch (InvalidOperationException)
                    {
                        // Process already disposed, skip termination
                        continue;
                    }

                    if (!hasExited)
                    {
                        try
                        {
                            kv.Value.Kill(true);
                            // Give process time to terminate
                            kv.Value.WaitForExit(1000);
                        }
                        catch (InvalidOperationException)
                        {
                            // Process became inaccessible during termination, ignore
                        }
                    }

                    // Note: Process disposal is handled by the caller's using statement
                    // We only terminate and remove from tracking here
                }
                catch (InvalidOperationException)
                {
                    // Process already disposed or inaccessible, ignore
                }
                catch (Exception ex)
                {
                    // Log termination errors for debugging
                    ErrorLogger.LogError("ProcessManager.TerminateAllProcesses", ex);
                }
                finally
                {
                    // Ensure removal from dictionary
                    _runningProcesses.TryRemove(kv.Key, out _);
                }
            }
        }
    }

    private static CancellationTokenSource GetOrCreateOperationCts(string operationId)
        => _operationTokens.GetOrAdd(operationId, _ => new CancellationTokenSource());

    private static void CleanupFinishedProcesses()
    {
        if (_disposed) return;

        var processesToRemove = new List<int>();

        foreach (var kv in _runningProcesses.ToList())
        {
            try
            {
                // Check if process has exited or is no longer accessible
                if (kv.Value.HasExited)
                {
                    processesToRemove.Add(kv.Key);
                    // Process disposal is handled by the caller's using statement
                    // We only remove it from tracking
                }
            }
            catch (InvalidOperationException)
            {
                // Process is no longer valid (likely disposed), mark for removal
                processesToRemove.Add(kv.Key);
            }
            catch (Exception)
            {
                // Handle any other exceptions, mark for removal
                processesToRemove.Add(kv.Key);
            }
        }

        // Remove all finished processes
        foreach (var processId in processesToRemove)
        {
            _runningProcesses.TryRemove(processId, out _);
        }
    }

    /// <summary>
    /// Executes a shell command with standard error handling and logging
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="arguments">Command arguments</param>
    /// <param name="workingDirectory">Working directory</param>
    /// <param name="operationId">Operation identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Command result with exit code and output</returns>
    public static async Task<(bool success, string output, string error)> ExecuteShellCommandAsync(
        string command,
        string arguments,
        string workingDirectory,
        string operationId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var (exitCode, stdOut, stdErr) = await RunProcessAsync(
                command, arguments, workingDirectory, operationId, cancellationToken);

            return (exitCode == 0, stdOut, stdErr);
        }
        catch (Exception ex)
        {
            ErrorLogger.LogError($"ProcessManager.ExecuteShellCommand.{operationId}", ex);
            return (false, string.Empty, ex.Message);
        }
    }

    /// <summary>
    /// Executes a command with timeout
    /// </summary>
    /// <param name="command">Command to execute</param>
    /// <param name="arguments">Command arguments</param>
    /// <param name="workingDirectory">Working directory</param>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <param name="operationId">Operation identifier</param>
    /// <returns>Command result</returns>
    public static async Task<(bool success, string output, string error)> ExecuteWithTimeoutAsync(
        string command,
        string arguments,
        string workingDirectory,
        int timeoutMs,
        string operationId)
    {
        using var cts = new CancellationTokenSource(timeoutMs);
        return await ExecuteShellCommandAsync(command, arguments, workingDirectory, operationId, cts.Token);
    }

    public static void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        // Dispose cleanup timer
        _cleanupTimer?.Dispose();

        // Terminate and dispose all processes
        TerminateAllProcesses();

        // Dispose all operation tokens
        foreach (var cts in _operationTokens.Values)
        {
            try
            {
                cts.Dispose();
            }
            catch
            {
                // Ignore disposal errors
            }
        }
        _operationTokens.Clear();

        // Clear running processes dictionary
        _runningProcesses.Clear();
    }
}