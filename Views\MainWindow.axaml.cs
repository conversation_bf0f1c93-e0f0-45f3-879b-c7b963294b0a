using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Documents;
using Avalonia.Interactivity;
using Avalonia.Layout;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Platform.Storage;
using Avalonia.Styling;
using Avalonia.Threading;
using SamsungTool.Library;
using SamsungTool.Library.GUI;
using SamsungTool.Library.Security;
using SamsungTool.Services.Interfaces;
using SamsungTool.Services;
using SamsungTool.Models;
using SamsungTool.Views;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool
{
    public partial class MainWindow : Window, IDisposable
    {
        public static MainWindow? GUI;
        private bool isOperationRunning = false;
        private readonly List<Button> operationButtons = new List<Button>();
        private DispatcherTimer? _progressTimer;
        private ThemeVariant _currentTheme = ThemeVariant.Dark;
        public bool IsDarkTheme => _currentTheme == ThemeVariant.Dark;
        private CancellationTokenSource? _currentOperationCts;
        private CancellationTokenSource? _flashCts;
        private string? _currentOperationId;
        private bool _disposed = false;
        private SecurityChecker? _securityChecker;
        private ILicenseService? _licenseService;
        private DispatcherTimer? _licenseTimer;
        private static readonly HttpClient _httpClient = new HttpClient();
        private static readonly string UpdateUrl = "https://samsungtool.service-app.org/updates/AutoUpdater.xml";
        private readonly Dictionary<string, int> _lastReportedProgress = new Dictionary<string, int>();
        private ILogManager? _logManager;
        private string? _currentLogSessionId;

        public MainWindow()
        {
            InitializeComponent();
            GUI = this;

            InitializeComponents();
            RegisterEventHandlers();
            RegisterOperationButtons();
            SetupTimers();
            InitializeLicenseService();
            InitializeLogManager();

            //_securityChecker = new SecurityChecker(this);
            //_securityChecker.StartMonitoring();

            this.Opened += OnWindowOpened;
            this.Closing += OnWindowClosing;

            // Check changelog on startup
            _ = Task.Run(async () => await CheckAndDisplayChangelogAsync().ConfigureAwait(false));

            // Start entrance animations after a short delay
            _ = Task.Run(async () =>
            {
                await Task.Delay(500); // Wait for window to fully load
                await StartMainWindowAnimationsAsync().ConfigureAwait(false);
            });
        }

        private void OnWindowOpened(object? sender, EventArgs e)
        {
            Program.SetupDeviceWatchers();
            USB.GetComInfo();
        }

        private void OnWindowClosing(object? sender, WindowClosingEventArgs e)
        {
            try
            {
                _ = Task.Run(() =>
                {
                    try
                    {
                        OnApplicationClosing();
                    }
                    catch { }
                });
            }
            catch { }
        }

        private void OnApplicationClosing()
        {
            try
            {
                _progressTimer?.Stop();
                _licenseTimer?.Stop();

                if (isOperationRunning)
                {
                    try
                    {
                        _currentOperationCts?.Cancel();
                        _flashCts?.Cancel();
                        ProcessManager.TerminateAllProcesses();
                    }
                    catch { }
                }

                try
                {
                    SamsungTool.Services.Implementations.MtpService.CleanupStaticPort();
                }
                catch { }

                try
                {
                    USB.DisposeWatcher();
                }
                catch { }

                try
                {
                    _securityChecker?.StopMonitoring();
                    _securityChecker?.Dispose();
                }
                catch { }

                try
                {
                    UnsubscribeAllEvents();
                }
                catch { }

                try
                {
                    _currentOperationCts?.Dispose();
                    _flashCts?.Dispose();
                }
                catch { }
            }
            catch { }
        }

        private void UnsubscribeAllEvents()
        {
            try
            {
                this.Opened -= OnWindowOpened;
                this.Closing -= OnWindowClosing;
                
                if (_licenseTimer != null)
                    _licenseTimer.Tick -= LicenseTimer_Tick;
                if (tabListBox != null)
                    tabListBox.SelectionChanged -= TabListBox_SelectionChanged;

                // MTP/ADB operation buttons
                if (btnReadinfoMTP != null) btnReadinfoMTP.Click -= MTPADB;
                if (btnRebootdownloadmtp != null) btnRebootdownloadmtp.Click -= MTPADB;
                if (btnQROS13 != null) btnQROS13.Click -= MTPADB;
                if (btnQROS14 != null) btnQROS14.Click -= MTPADB;
                if (btnChangeColorMTP != null) btnChangeColorMTP.Click -= MTPADB;
                if (btnRebootNormalMTP != null) btnRebootNormalMTP.Click -= MTPADB;
                if (btnReadInfoADB != null) btnReadInfoADB.Click -= MTPADB;
                if (btnRemoveFRPADB != null) btnRemoveFRPADB.Click -= MTPADB;
                if (btnRebootDownloadADB != null) btnRebootDownloadADB.Click -= MTPADB;
                if (btnRebootrecovery != null) btnRebootrecovery.Click -= MTPADB;
                if (btnKGOS14 != null) btnKGOS14.Click -= MTPADB;
                if (btnKGApps != null) btnKGApps.Click -= MTPADB;
                if (btnKGRemove2025 != null) btnKGRemove2025.Click -= MTPADB;
                if (btnChangeCSC != null) btnChangeCSC.Click -= MTPADB;
                if (btnChangeSerial != null) btnChangeSerial.Click -= MTPADB;

                // Odin mode buttons
                if (btnReadinfo != null) btnReadinfo.Click -= OdinMode;
                if (btnFixSoftBrick != null) btnFixSoftBrick.Click -= OdinMode;
                if (btnFactoryReset != null) btnFactoryReset.Click -= OdinMode;
                if (btnRemoveFRPMTK != null) btnRemoveFRPMTK.Click -= OdinMode;

                // Flash operations
                if (btnClearFirmware != null) btnClearFirmware.Click -= btnClearFirmware_Click;
                if (btnClearLogs != null) btnClearLogs.Click -= BtnClearLogs_Click;
                if (btnHistory != null) btnHistory.Click -= BtnHistory_Click;
                if (btnBL != null) btnBL.Click -= btnBL_Click;
                if (btnAP != null) btnAP.Click -= btnAP_Click;
                if (btnCP != null) btnCP.Click -= btnCP_Click;
                if (btnCSC != null) btnCSC.Click -= btnCSC_Click;
                if (btnHome != null) btnHome.Click -= btnHome_Click;
                if (btnFlash != null) btnFlash.Click -= btnFlash_Click;
                if (btnCancelOperation != null) btnCancelOperation.Click -= CancelOperation_Click;

                if (cmbCOM != null) cmbCOM.SelectionChanged -= cmbCOM_SelectionChanged;
                if (_progressTimer != null) _progressTimer.Tick -= ProgressTimer_Tick;
            }
            catch { }
        }

        private void InitializeComponents()
        {
            if (tabListBox != null)
            {
                tabListBox.SelectionChanged += TabListBox_SelectionChanged;
                tabListBox.SelectedIndex = 0;
            }
            UpdateContentAreaForSelectedTab();

            _currentTheme = ThemeVariant.Dark;
            if (Application.Current != null)
                Application.Current.RequestedThemeVariant = ThemeVariant.Dark;
        }

        private void RegisterEventHandlers()
        {
            // MTP operations
            if (btnReadinfoMTP != null) btnReadinfoMTP.Click += MTPADB;
            if (btnRebootdownloadmtp != null) btnRebootdownloadmtp.Click += MTPADB;
            if (btnQROS13 != null) btnQROS13.Click += MTPADB;
            if (btnQROS14 != null) btnQROS14.Click += MTPADB;
            if (btnChangeColorMTP != null) btnChangeColorMTP.Click += MTPADB;
            if (btnRebootNormalMTP != null) btnRebootNormalMTP.Click += MTPADB;

            // ADB operations  
            if (btnReadInfoADB != null) btnReadInfoADB.Click += MTPADB;
            if (btnRemoveFRPADB != null) btnRemoveFRPADB.Click += MTPADB;
            if (btnRebootDownloadADB != null) btnRebootDownloadADB.Click += MTPADB;
            if (btnRebootrecovery != null) btnRebootrecovery.Click += MTPADB;
            if (btnKGOS14 != null) btnKGOS14.Click += MTPADB;
            if (btnKGApps != null) btnKGApps.Click += MTPADB;
            if (btnKGRemove2025 != null) btnKGRemove2025.Click += MTPADB;
            if (btnChangeCSC != null) btnChangeCSC.Click += MTPADB;
            if (btnChangeSerial != null) btnChangeSerial.Click += MTPADB;

            // Odin operations
            if (btnReadinfo != null) btnReadinfo.Click += OdinMode;
            if (btnFixSoftBrick != null) btnFixSoftBrick.Click += OdinMode;
            if (btnFactoryReset != null) btnFactoryReset.Click += OdinMode;
            if (btnRemoveFRPMTK != null) btnRemoveFRPMTK.Click += OdinMode;

            // Flash operations
            if (btnClearFirmware != null) btnClearFirmware.Click += btnClearFirmware_Click;
            if (btnClearLogs != null) btnClearLogs.Click += BtnClearLogs_Click;
            if (btnHistory != null) btnHistory.Click += BtnHistory_Click;
            if (btnBL != null) btnBL.Click += btnBL_Click;
            if (btnAP != null) btnAP.Click += btnAP_Click;
            if (btnCP != null) btnCP.Click += btnCP_Click;
            if (btnCSC != null) btnCSC.Click += btnCSC_Click;
            if (btnHome != null) btnHome.Click += btnHome_Click;
            if (btnFlash != null) btnFlash.Click += btnFlash_Click;
            if (btnCancelOperation != null) btnCancelOperation.Click += CancelOperation_Click;

            if (cmbCOM != null) cmbCOM.SelectionChanged += cmbCOM_SelectionChanged;
        }

        private void RegisterOperationButtons()
        {
            var buttons = new[]
            {
                btnReadinfoMTP, btnRebootdownloadmtp, btnQROS13, btnQROS14, btnChangeColorMTP, btnRebootNormalMTP,
                btnReadInfoADB, btnRemoveFRPADB, btnRebootDownloadADB, btnChangeCSC, btnChangeSerial,
                btnRebootrecovery, btnKGOS14, btnKGApps, btnKGRemove2025,
                btnReadinfo, btnFixSoftBrick, btnFactoryReset, btnRemoveFRPMTK,
                btnFlash
            };

            operationButtons.AddRange(buttons.Where(b => b != null).Cast<Button>());
        }

        private void SetupTimers()
        {
            _progressTimer = new DispatcherTimer();
            _progressTimer.Interval = TimeSpan.FromSeconds(2);
            _progressTimer.Tick += ProgressTimer_Tick;
        }

        private void InitializeLogManager()
        {
            try
            {
                _logManager = ServiceLocator.GetService<ILogManager>();
            }
            catch (Exception ex)
            {
                // Log manager initialization failed, continue without it
                RichLogs($"Warning: Log manager initialization failed: {ex.Message}", ConvertColor(Colors.Orange), true);
            }
        }

        private void StartLogSession(string operationName)
        {
            try
            {
                if (_logManager != null && !string.IsNullOrEmpty(operationName))
                {
                    var operationType = DetermineOperationType(operationName);
                    _currentLogSessionId = _logManager.StartLogSession(operationName, operationType);
                }
            }
            catch (Exception ex)
            {
                // Log session start failed, continue silently to avoid spamming user
                ErrorLogger.LogError("MainWindow.StartLogSession", ex);
                _currentLogSessionId = null;
            }
        }

        private async Task EndLogSessionAsync()
        {
            try
            {
                if (_logManager != null && !string.IsNullOrEmpty(_currentLogSessionId))
                {
                    // Add delay to ensure all async UI updates complete
                    // This is especially important for operations that display device info
                    await Task.Delay(1000);

                    var logContent = GetCurrentLogContent();
                    var status = DetermineOperationStatus();

                    await _logManager.EndLogSessionAsync(_currentLogSessionId, status, logContent);
                    _currentLogSessionId = null;
                }
            }
            catch (Exception ex)
            {
                // Log session end failed, continue silently to avoid spamming user
                // Only log to debug/error logger, not to UI
                ErrorLogger.LogError("MainWindow.EndLogSessionAsync", ex);
                _currentLogSessionId = null; // Reset to prevent repeated attempts
            }
        }

        private string DetermineOperationType(string operationName)
        {
            // Check for ADB operations first (more specific)
            if (operationName.Contains("[ ADB ]"))
                return "ADB";

            // Check for MTP operations
            if (operationName.Contains("[ MTP ]") || operationName.Contains("MTP") ||
                operationName.Contains("Reboot") || operationName.Contains("QR"))
                return "MTP";

            // Check for other ADB operations
            if (operationName.Contains("ADB") || operationName.Contains("FRP") ||
                operationName.Contains("KG") || operationName.Contains("CSC") ||
                operationName.Contains("Serial"))
                return "ADB";

            if (operationName.Contains("Flash"))
                return "Flash";

            if (operationName.Contains("SoftBrick") || operationName.Contains("Factory Reset") ||
                operationName.Contains("MTK"))
                return "Download Mode";

            // Default for Reading Information without specific tag
            if (operationName.Contains("Reading Information"))
                return "MTP";

            return "Unknown";
        }

        private LogEntryStatus DetermineOperationStatus()
        {
            // Simple heuristic based on current log content
            var logContent = GetCurrentLogContent().ToLower();

            if (logContent.Contains("error") || logContent.Contains("failed") || logContent.Contains("exception"))
                return LogEntryStatus.Failed;

            if (logContent.Contains("cancel") || logContent.Contains("abort"))
                return LogEntryStatus.Cancelled;

            if (logContent.Contains("success") || logContent.Contains("completed") || logContent.Contains("done"))
                return LogEntryStatus.Success;

            // Default to success if no clear indicators
            return LogEntryStatus.Success;
        }

        private string GetCurrentLogContent()
        {
            try
            {
                // Ensure we're on the UI thread
                if (!Dispatcher.UIThread.CheckAccess())
                {
                    return Dispatcher.UIThread.Invoke(() => GetCurrentLogContent());
                }

                if (Richlogs?.Inlines != null && Richlogs.Inlines.Count > 0)
                {
                    var content = new System.Text.StringBuilder();

                    foreach (var inline in Richlogs.Inlines)
                    {
                        // Handle direct Run elements (from UI.RichLogs)
                        if (inline is Run run && !string.IsNullOrEmpty(run.Text))
                        {
                            content.Append(run.Text);
                        }
                        // Handle Span elements (from MainWindow.RichLogs)
                        else if (inline is Span span && span.Inlines != null)
                        {
                            foreach (var spanInline in span.Inlines)
                            {
                                if (spanInline is Run spanRun && !string.IsNullOrEmpty(spanRun.Text))
                                {
                                    content.Append(spanRun.Text);
                                }
                                else if (spanInline is LineBreak)
                                {
                                    content.AppendLine();
                                }
                            }
                        }
                        // Handle direct LineBreak elements
                        else if (inline is LineBreak)
                        {
                            content.AppendLine();
                        }
                    }

                    return content.ToString();
                }

                // Fallback to Text property if Inlines is empty
                if (!string.IsNullOrEmpty(Richlogs?.Text))
                    return Richlogs.Text;

                return string.Empty;
            }
            catch (Exception ex)
            {
                // Debug: log the exception to understand what's happening
                System.Diagnostics.Debug.WriteLine($"GetCurrentLogContent error: {ex.Message}");
                return string.Empty;
            }
        }

        private void TabListBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            // Smooth tab transition with hide-then-show effect
            if (tabListBox?.SelectedIndex is not int selectedIndex || selectedIndex < 0)
                return;

            // Start smooth tab transition animation
            _ = Task.Run(async () =>
            {
                await Dispatcher.UIThread.InvokeAsync(async () =>
                {
                    await SmoothTabTransitionAsync(selectedIndex);
                });
            });
        }

        private async Task SmoothTabTransitionAsync(int selectedIndex)
        {
            try
            {
                var allContents = new[] { functionsContent, odinContent, advancedContent };
                var currentVisibleContent = allContents.FirstOrDefault(c => c?.IsVisible == true);
                var targetContent = allContents[selectedIndex];

                if (currentVisibleContent == targetContent)
                    return; // Already on the target tab

                // Determine slide direction
                var currentIndex = Array.IndexOf(allContents, currentVisibleContent);
                var isMovingRight = selectedIndex > currentIndex;

                // Phase 1: Slide out current content (thu hồi tab cũ)
                if (currentVisibleContent != null)
                {
                    currentVisibleContent.Classes.Clear();
                    if (isMovingRight)
                    {
                        // Tab cũ slide ra trái
                        currentVisibleContent.Classes.Add("slide-out-left");
                    }
                    else
                    {
                        // Tab cũ slide ra phải
                        currentVisibleContent.Classes.Add("slide-out-right");
                    }
                }

                // Wait for slide out to complete
                await Task.Delay(200);

                // Phase 2: Hide old content and prepare new content
                if (currentVisibleContent != null)
                {
                    currentVisibleContent.IsVisible = false;
                    currentVisibleContent.Classes.Clear();
                }

                if (targetContent != null)
                {
                    // Prepare new content off-screen
                    targetContent.Classes.Clear();
                    if (isMovingRight)
                    {
                        // Tab mới bắt đầu từ trái
                        targetContent.RenderTransform = new TranslateTransform(-80, 0);
                    }
                    else
                    {
                        // Tab mới bắt đầu từ phải
                        targetContent.RenderTransform = new TranslateTransform(80, 0);
                    }

                    targetContent.Opacity = 0;
                    targetContent.IsVisible = true;

                    // Phase 3: Slide in new content (hiện tab mới)
                    await Task.Delay(30);

                    if (isMovingRight)
                    {
                        targetContent.Classes.Add("slide-in-from-left");
                    }
                    else
                    {
                        targetContent.Classes.Add("slide-in-from-right");
                    }
                }

                // Wait for slide in to complete
                await Task.Delay(250);

                // Clean up
                if (targetContent != null)
                {
                    targetContent.Classes.Clear();
                    targetContent.RenderTransform = new TranslateTransform(0, 0);
                    targetContent.Opacity = 1;
                }

                // Update title
                UpdateContentTitle(selectedIndex);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Smooth slide transition error: {ex.Message}");
                // Fallback to immediate switching
                UpdateContentAreaForSelectedTab();
            }
        }

        private void UpdateContentTitle(int selectedIndex)
        {
            try
            {
                if (tabListBox?.SelectedItem is ListBoxItem selectedItem &&
                    selectedItem.Content is StackPanel stackPanel)
                {
                    foreach (var child in stackPanel.Children)
                    {
                        if (child is TextBlock textBlock && contentTitle != null)
                        {
                            contentTitle.Text = textBlock.Text;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Update content title error: {ex.Message}");
            }
        }

        private void UpdateContentAreaForSelectedTab()
        {
            if (tabListBox?.SelectedIndex is not int selectedIndex || selectedIndex < 0)
                return;

            var selectedItem = tabListBox.SelectedItem as ListBoxItem;
            if (selectedItem?.Content is StackPanel stackPanel)
                    {
                        foreach (var child in stackPanel.Children)
                        {
                            if (child is TextBlock textBlock && contentTitle != null)
                            {
                                contentTitle.Text = textBlock.Text;
                                break;
                            }
                        }
                    }

            System.Diagnostics.Debug.WriteLine($"Updating content visibility for tab {selectedIndex}");

            if (functionsContent != null)
            {
                functionsContent.IsVisible = selectedIndex == 0;
                System.Diagnostics.Debug.WriteLine($"Functions content visible: {functionsContent.IsVisible}");
            }
            if (odinContent != null)
            {
                odinContent.IsVisible = selectedIndex == 1;
                System.Diagnostics.Debug.WriteLine($"Odin content visible: {odinContent.IsVisible}");
            }
            if (advancedContent != null)
            {
                advancedContent.IsVisible = selectedIndex == 2;
                System.Diagnostics.Debug.WriteLine($"Advanced content visible: {advancedContent.IsVisible}");
            }
        }

        private async Task FastAnimateContentSwitchAsync(int selectedIndex)
        {
            try
            {
                // Immediate visibility update for instant response
                UpdateContentAreaForSelectedTab();

                // Apply smooth entrance animation to visible content
                var selectedContent = selectedIndex switch
                {
                    0 => functionsContent,
                    1 => odinContent,
                    2 => advancedContent,
                    _ => null
                };

                if (selectedContent != null)
                {
                    // Reset animation state
                    selectedContent.Classes.Remove("content-hide");
                    selectedContent.Classes.Remove("content-show");

                    // Trigger smooth entrance
                    await Task.Delay(10); // Minimal delay for CSS to apply
                    selectedContent.Classes.Add("content-show");

                    // Non-blocking content animation
                    _ = AnimateSelectedContentAsync(selectedIndex);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Fast content switch error: {ex.Message}");
                // Immediate fallback
                UpdateContentAreaForSelectedTab();
            }
        }



        private async Task AnimateSelectedContentAsync(int selectedIndex)
        {
            try
            {
                // Simple content animation without complex logic
                await Task.Delay(10); // Minimal delay for smooth effect

                switch (selectedIndex)
                {
                    case 0:
                        if (functionsContent != null)
                            _ = AnimateFunctionsContentAsync();
                        break;
                    case 1:
                        if (odinContent != null)
                            _ = AnimateOdinContentAsync();
                        break;
                    case 2:
                        if (advancedContent != null)
                            _ = AnimateAdvancedContentAsync();
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Selected content animation error: {ex.Message}");
            }
        }

        private void SetOperationInProgress(bool inProgress, string operationName = "")
        {
            isOperationRunning = inProgress;

            foreach (var button in operationButtons)
            {
                if (button != null)
                button.IsEnabled = !inProgress;
            }

            if (btnCancelOperation != null)
            btnCancelOperation.IsEnabled = inProgress;
            if (cmbCOM != null)
            cmbCOM.IsEnabled = !inProgress;

            if (inProgress)
            {
                _currentOperationId = Guid.NewGuid().ToString();
                _currentOperationCts = new CancellationTokenSource();

                if (operationStatus != null)
                operationStatus.Text = string.IsNullOrEmpty(operationName) ? "Operation in progress..." : $"Running: {operationName}";

                if (Loading != null)
                {
                Loading.Value = 15;
                Loading.IsVisible = true;
                }

                // Start log session
                StartLogSession(operationName);
            }
            else
            {
                if (operationStatus != null)
                operationStatus.Text = "Ready";
                if (Loading != null)
                {
                Loading.IsVisible = false;
                Loading.Value = 0;
                }

                // End log session
                _ = Task.Run(() => EndLogSessionAsync());

                _currentOperationCts?.Dispose();
                _currentOperationCts = null;
                _currentOperationId = null;
            }
        }

        private void CancelOperation_Click(object? sender, RoutedEventArgs e)
        {
            CancelCurrentOperation();
        }

        private void CancelCurrentOperation()
        {
            if (isOperationRunning)
            {
                try
                {
                    RichLogs("\r\nCancelling operation...", ConvertColor(Colors.Orange), true, 13);
                    _currentOperationCts?.Cancel();
                    _flashCts?.Cancel();

                    if (!string.IsNullOrEmpty(_currentOperationId))
                    {
                        try { ProcessManager.CancelOperation(_currentOperationId); } catch { }
                    }

                    ProcessManager.TerminateAllProcesses();
                }
                catch (Exception ex)
                {
                    if (!ex.Message.Contains("process") && !ex.Message.Contains("cancelled"))
                    {
                        RichLogs($"Error during cancellation: {ex.Message}", ConvertColor(Colors.Red), true);
                    }
                }
            }
        }

        private void ProgressTimer_Tick(object? sender, EventArgs e)
        {
            if (!isOperationRunning)
            {
                _progressTimer?.Stop();
                return;
            }

            if (Loading?.Value < 90)
                Loading.Value += 5;
        }

        private void UpdateUi(ProgressReport report)
        {
            Dispatcher.UIThread.Post(() =>
            {
                if (report != null && Loading != null)
                {
                    Loading.Value = report.PercentComplete;

                    string fileName = !string.IsNullOrEmpty(report.CurrentFile)
                        ? Path.GetFileName(report.CurrentFile)
                        : "Unknown";

                    if (operationStatus != null)
                    operationStatus.Text = $"Flashing {fileName}: {report.PercentComplete}%";

                    if (report.PercentComplete > 0)
                    {
                        if (!_lastReportedProgress.TryGetValue(fileName, out int lastProgress) ||
                            lastProgress != report.PercentComplete)
                        {
                            if (report.PercentComplete == 25 ||
                                report.PercentComplete == 50 ||
                                report.PercentComplete == 75)
                            {
                                _lastReportedProgress[fileName] = report.PercentComplete;
                                var avaloniaColor = Colors.SkyBlue;
                                var color = ConvertColor(avaloniaColor);
                                RichLogs($"{report.PercentComplete}% ", color, false);
                            }
                        }
                    }
                }
            });
        }

        private void ResetFlashProgress()
        {
            _lastReportedProgress.Clear();
        }

        private static System.Drawing.Color ConvertColor(Avalonia.Media.Color avaloniaColor)
        {
            return System.Drawing.Color.FromArgb(
                avaloniaColor.A,
                avaloniaColor.R,
                avaloniaColor.G,
                avaloniaColor.B);
        }

        private async void MTPADB(object? sender, RoutedEventArgs e)
        {
            // Check token before operation
            if (!await CheckTokenBeforeOperationAsync()) return;

            if (isOperationRunning)
            {
                await ShowMessageBoxAsync("An operation is already in progress. Please wait for it to complete.", "Operation in Progress");
                return;
            }

            if (sender is not Button btn) return;

            string operationName = btn.Content?.ToString() ?? "Unknown Operation";
            SetOperationInProgress(true, operationName);

            ClearLog();
            RichLogs($"Operation: {operationName}", ConvertColor(Colors.Silver), true, 13);

            try
            {
                var cancellationToken = _currentOperationCts?.Token ?? CancellationToken.None;

                switch (operationName)
                {
                    case "Reading Information [ MTP ]":
                        await Thread_handling.MTPThread("readinfo", cancellationToken: cancellationToken);
                        break;
                    case "Reboot to Download Mode [ MTP ]":
                        await Thread_handling.MTPThread("rebootdownload", cancellationToken: cancellationToken);
                        break;
                    case "Factory Reset [ MTP ]":
                        await Thread_handling.MTPThread("factoryreset", cancellationToken: cancellationToken);
                        break;
                    case "QR CODE [ MTP ]":
                        await Thread_handling.MTPThread("QR", 14, cancellationToken);
                        break;
                    case "Change Color Code [ MTP ]":
                        await Thread_handling.MTPThread("changecolor", cancellationToken: cancellationToken);
                        break;
                    case "Reboot Normal [ MTP ]":
                        await Thread_handling.MTPThread("rebotnormal", cancellationToken: cancellationToken);
                        break;
                    case "Reading Information [ ADB ]":
                        await Thread_handling.ADBThread("readinfo", cancellationToken);
                        break;
                    case "Remove FRP [ ADB ]":
                        await Thread_handling.ADBThread("frp", cancellationToken);
                        break;
                    case "Reboot to Download Mode [ ADB ]":
                        await Thread_handling.ADBThread("rebootdownload", cancellationToken);
                        break;
                    case "Reboot to Recovery [ ADB ]":
                        await Thread_handling.ADBThread("rebootrecovery", cancellationToken);
                        break;
                    case "KG Bypass [10 - 2024] [ ADB ]":
                        await Thread_handling.ADBThread("kgos14", cancellationToken);
                        break;
                    case "KG Bypass 2025 [ ADB ]":
                        await Thread_handling.ADBThread("kgapps", cancellationToken);
                        break;
                    case "KG Remove ALL OS 14 [ 06 - 2025 ] [ ADB ]":
                        await Thread_handling.KgRemove2025(cancellationToken);
                        break;
                    case "Change CSC [ 06 - 2025 ] [ ADB ]":
                        await Thread_handling.ADBThread("changecsc", cancellationToken);
                        break;
                    case "Change Serial [ 06 - 2025 ] [ ADB ]":
                        await Thread_handling.ADBThread("changeserial", cancellationToken);
                        break;
                }
            }
            catch (OperationCanceledException)
            {
            }
            catch (Exception ex)
            {
                if (!ex.Message.Contains("cancel") && !ex.Message.Contains("abort"))
                {
                    RichLogs($"Error: {ex.Message}", ConvertColor(Colors.Red), true);
                }
            }
            finally
            {
                SetOperationInProgress(false);
            }
        }

        private async void OdinMode(object? sender, RoutedEventArgs e)
        {
            // Check token before operation
            if (!await CheckTokenBeforeOperationAsync()) return;

            if (isOperationRunning)
            {
                await ShowMessageBoxAsync("An operation is already in progress. Please wait for it to complete.", "Operation in Progress");
                return;
            }

            if (sender is not Button btn) return;

            string operationName = btn.Content?.ToString() ?? "Unknown Operation";
            SetOperationInProgress(true, operationName);

            ClearLog();

            try
            {
                var cancellationToken = _currentOperationCts?.Token ?? CancellationToken.None;

                switch (operationName)
                {
                    case "Reading Information":
                        await Thread_handling.DownloadmodeThread("readinfo", cancellationToken);
                        break;
                    case "Fix SoftBrick":
                        await Thread_handling.DownloadmodeThread("softbrick", cancellationToken);
                        break;
                    case "Factory Reset":
                        await Thread_handling.DownloadmodeThread("factoryreset", cancellationToken);
                        break;
                    case "Remove FRP MTK":
                        await Thread_handling.DownloadmodeThread("frpmtk", cancellationToken);
                        break;
                }
            }
            catch (OperationCanceledException)
            {
            }
            catch (Exception ex)
            {
                if (!ex.Message.Contains("cancel") && !ex.Message.Contains("abort"))
                {
                    RichLogs($"Error: {ex.Message}", ConvertColor(Colors.Red), true);
                }
            }
            finally
            {
                SetOperationInProgress(false);
            }
        }

        private void btnClearFirmware_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashBL != null) txtFlashBL.Text = "";
            if (txtFlashAP != null) txtFlashAP.Text = "";
            if (txtFlashCP != null) txtFlashCP.Text = "";
            if (txtFlashCSC != null) txtFlashCSC.Text = "";
            if (txtFlashDATA != null) txtFlashDATA.Text = "";
            if (cbFlashBL != null) cbFlashBL.IsChecked = false;
            if (cbFlashAP != null) cbFlashAP.IsChecked = false;
            if (cbFlashCP != null) cbFlashCP.IsChecked = false;
            if (cbFlashCSC != null) cbFlashCSC.IsChecked = false;
            if (cbFlashDATA != null) cbFlashDATA.IsChecked = false;
            ClearLog();
        }

        private void cmbCOM_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            // Simplified COM selection handling
        }

        private async Task<string> OpenFirmwareDialog(TextBox textBox)
        {
            try
            {
                var storageProvider = StorageProvider;
                if (storageProvider == null) return "";

                var options = new FilePickerOpenOptions
                {
                    Title = "Open the firmware file",
                    FileTypeFilter = new[]
                    {
                        new FilePickerFileType("TAR or TAR.MD5")
                        {
                            Patterns = new[] { "*.tar", "*.tar.md5" }
                        }
                    },
                    AllowMultiple = false
                };

                var result = await storageProvider.OpenFilePickerAsync(options);
                if (result.Count > 0)
                {
                    string selectedFile = result[0].Path.LocalPath;
                    textBox.Text = selectedFile;
                    return Path.GetFileName(selectedFile);
                }
            }
            catch
            {
                // StorageProvider failed
            }
            return "";
        }

        private async void btnBL_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashBL != null)
        {
            string text = await OpenFirmwareDialog(txtFlashBL);
                if (text != "" && cbFlashBL != null)
            {
                cbFlashBL.IsChecked = true;
                }
            }
        }

        private async void btnAP_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashAP != null)
        {
            string text = await OpenFirmwareDialog(txtFlashAP);
                if (text != "" && cbFlashAP != null)
            {
                cbFlashAP.IsChecked = true;
                }
            }
        }

        private async void btnCP_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashCP != null)
        {
            string text = await OpenFirmwareDialog(txtFlashCP);
                if (text != "" && cbFlashCP != null)
            {
                cbFlashCP.IsChecked = true;
                }
            }
        }

        private async void btnCSC_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashCSC != null)
        {
            string text = await OpenFirmwareDialog(txtFlashCSC);
                if (text != "" && cbFlashCSC != null)
            {
                cbFlashCSC.IsChecked = true;
                }
            }
        }

        private async void btnHome_Click(object? sender, RoutedEventArgs e)
        {
            if (txtFlashDATA != null)
        {
            string text = await OpenFirmwareDialog(txtFlashDATA);
                if (text != "" && cbFlashDATA != null)
            {
                cbFlashDATA.IsChecked = true;
                }
            }
        }

        public void UpdateOperationStatus(string status)
        {
            Dispatcher.UIThread.Post(() =>
            {
                if (operationStatus != null)
                operationStatus.Text = status;
            });
        }

        private async void btnFlash_Click(object? sender, RoutedEventArgs e)
        {
            // Check token before operation
            if (!await CheckTokenBeforeOperationAsync()) return;

            if (isOperationRunning)
            {
                await ShowMessageBoxAsync("An operation is already in progress. Please wait for it to complete.", "Operation in Progress");
                return;
            }

            SetOperationInProgress(true, "Flash Firmware");

            try
            {
                ClearLog();
                ResetFlashProgress();
                _flashCts = new CancellationTokenSource();
                await Thread_handling.StartFlashAsync(
                    new Progress<ProgressReport>(UpdateUi),
                    _flashCts.Token);
            }
            catch (OperationCanceledException)
            {
            }
            catch (Exception ex)
            {
                if (!ex.Message.Contains("cancel") && !ex.Message.Contains("abort"))
                {
                    RichLogs($"Error during flash: {ex.Message}", ConvertColor(Colors.Red), true);
                }
            }
            finally
            {
                SetOperationInProgress(false);
            }
        }

        private void BtnClearLogs_Click(object? sender, RoutedEventArgs e)
        {
            if (Richlogs != null)
        {
            Richlogs.Text = "";
            Richlogs.Text += "Logs cleared.\n";
            }
        }

        private async void BtnHistory_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                var historyWindow = new HistoryWindow();
                await historyWindow.ShowDialog(this);
            }
            catch (Exception ex)
            {
                await ShowMessageBoxAsync($"Error opening history window: {ex.Message}", "Error");
            }
        }

        private async Task ShowMessageBoxAsync(string message, string title)
        {
            var messageBox = new Window
            {
                Title = title,
                Width = 320,
                Height = 160,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Background = this.Background
            };

            var panel = new StackPanel
            {
                Margin = new Thickness(20),
                VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                Spacing = 20
            };

            var textBlock = new TextBlock
            {
                Text = message,
                TextWrapping = TextWrapping.Wrap,
                HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center
            };

            var button = new Button
            {
                Content = "OK",
                HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                Width = 100
            };

            button.Click += (s, e) => messageBox.Close();

            panel.Children.Add(textBlock);
            panel.Children.Add(button);

            messageBox.Content = panel;

            await messageBox.ShowDialog(this);
        }

        public async Task<string?> ShowInputDialogAsync(string title, string message, string? description = null)
        {
            return await InputSerial.ShowAsync(this, title, message, description);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                try
                {
                    _securityChecker?.StopMonitoring();
                    _securityChecker?.Dispose();
                    _securityChecker = null;

                    UnsubscribeAllEvents();

                    _progressTimer?.Stop();
                    _progressTimer = null;

                    _licenseTimer?.Stop();
                    _licenseTimer = null;

                    _currentOperationCts?.Dispose();
                    _currentOperationCts = null;

                    _flashCts?.Dispose();
                    _flashCts = null;

                    try
                    {
                        _httpClient?.Dispose();
                    }
                    catch { }
                }
                catch { }
            }

            _disposed = true;
        }

        ~MainWindow()
        {
            Dispose(false);
        }

        private void RichLogs(string message, System.Drawing.Color color, bool addNewLine, double fontSize = 14)
        {
            Dispatcher.UIThread.Post(() => {
                if (Richlogs == null) return;

                var avaloniaColor = Color.FromArgb(
                    color.A,
                    color.R,
                    color.G,
                    color.B);

                var textRun = new Run { Text = message + (addNewLine ? Environment.NewLine : "") };
                var inline = new Span
                {
                    Foreground = new SolidColorBrush(avaloniaColor),
                    FontSize = fontSize
                };
                inline.Inlines.Add(textRun);

                if (Richlogs.Inlines == null)
                    Richlogs.Inlines = new Avalonia.Controls.Documents.InlineCollection();

                Richlogs.Inlines.Add(inline);

                // Update current log session
                UpdateCurrentLogSession();
            });
        }

        private void ClearLog()
        {
            Dispatcher.UIThread.Post(() => {
                if (Richlogs != null)
                {
                    Richlogs.Text = string.Empty;
                    if (Richlogs.Inlines != null)
                        Richlogs.Inlines.Clear();
                }

                // Update current log session if active (must be on UI thread)
                UpdateCurrentLogSession();
            });
        }

        private void UpdateCurrentLogSession()
        {
            try
            {
                if (_logManager != null && !string.IsNullOrEmpty(_currentLogSessionId))
                {
                    var logContent = GetCurrentLogContent();
                    _logManager.UpdateLogContent(_currentLogSessionId, logContent ?? string.Empty);
                }
            }
            catch (Exception ex)
            {
                // Log the exception for debugging if needed
                System.Diagnostics.Debug.WriteLine($"UpdateCurrentLogSession error: {ex.Message}");
            }
        }

        private void InitializeLicenseService()
        {
            _licenseService = ServiceLocator.GetService<ILicenseService>();
            
            _licenseTimer = new DispatcherTimer();
            _licenseTimer.Interval = TimeSpan.FromMinutes(30); // Only for license check
            _licenseTimer.Tick += LicenseTimer_Tick;
            _licenseTimer.Start();

            _ = Task.Run(async () => await CheckLicenseStatusAsync().ConfigureAwait(false));
        }

        private async void LicenseTimer_Tick(object? sender, EventArgs e)
        {
            await CheckLicenseStatusAsync().ConfigureAwait(false);
        }

        private async Task CheckLicenseStatusAsync()
        {
            try
            {
                if (_licenseService != null)
            {
                var licenseInfo = await _licenseService.GetLicenseInfoAsync();
                
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    UpdateLicenseDisplay(licenseInfo);
                });
            }
            }
            catch
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    UpdateLicenseDisplay(null);
                });
            }
        }

        private void UpdateLicenseDisplay(LicenseInfo? licenseInfo)
        {
            if (licenseInfo != null && licenseInfo.Status)
            {
                if (licenseUsername != null)
                licenseUsername.Text = licenseInfo.Username ?? "User";
                
                if (licenseInfo.ExpirationDate.HasValue)
                {
                    var expiration = licenseInfo.ExpirationDate.Value;
                    var timeRemaining = expiration - DateTime.Now;
                    
                    if (timeRemaining.TotalDays > 0)
                    {
                        if (timeRemaining.TotalDays > 30)
                        {
                            if (licenseExpiration != null)
                        {
                            licenseExpiration.Text = expiration.ToString("MMM dd, yyyy");
                                licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                            }
                            if (licenseStatus != null)
                            {
                            licenseStatus.Text = "Active";
                            licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                            }
                            if (licenseStatusIndicator != null)
                            licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                            if (licenseStatusIcon != null)
                            licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                        }
                        else if (timeRemaining.TotalDays > 7)
                        {
                            if (licenseExpiration != null)
                        {
                            licenseExpiration.Text = $"{(int)timeRemaining.TotalDays} days left";
                                licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                            }
                            if (licenseStatus != null)
                            {
                            licenseStatus.Text = "Expiring Soon";
                            licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                            }
                            if (licenseStatusIndicator != null)
                            licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                            if (licenseStatusIcon != null)
                            licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                        }
                        else
                        {
                            if (licenseExpiration != null)
                        {
                            licenseExpiration.Text = $"{(int)timeRemaining.TotalDays} days left";
                                licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                            }
                            if (licenseStatus != null)
                            {
                            licenseStatus.Text = "Critical";
                            licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                            }
                            if (licenseStatusIndicator != null)
                            licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                            if (licenseStatusIcon != null)
                            licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                        }
                    }
                    else
                    {
                        if (licenseExpiration != null)
                        {
                        licenseExpiration.Text = "Expired";
                            licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                        }
                        if (licenseStatus != null)
                        {
                        licenseStatus.Text = "Expired";
                        licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                        }
                        if (licenseStatusIndicator != null)
                        licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                        if (licenseStatusIcon != null)
                        licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                    }
                }
                else
                {
                    if (licenseExpiration != null)
                {
                    licenseExpiration.Text = "Unknown";
                        licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(158, 158, 158));
                    }
                    if (licenseStatus != null)
                    {
                    licenseStatus.Text = "Unknown";
                    licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(158, 158, 158));
                    }
                    if (licenseStatusIndicator != null)
                    licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(158, 158, 158));
                    if (licenseStatusIcon != null)
                    licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(158, 158, 158));
                }
            }
            else
            {
                if (licenseUsername != null)
                licenseUsername.Text = "Unknown";
                if (licenseExpiration != null)
                {
                licenseExpiration.Text = "Invalid License";
                    licenseExpiration.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                }
                if (licenseStatus != null)
                {
                licenseStatus.Text = "Invalid";
                licenseStatus.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                }
                if (licenseStatusIndicator != null)
                licenseStatusIndicator.Fill = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                if (licenseStatusIcon != null)
                licenseStatusIcon.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            }
        }

        private async Task StartMainWindowAnimationsAsync()
        {
            try
            {
                await Dispatcher.UIThread.InvokeAsync(async () =>
                {
                    // Animate sidebar first
                    var sidebarBorder = this.FindControl<Border>("SidebarBorder");
                    if (sidebarBorder != null)
                    {
                        sidebarBorder.Classes.Add("loaded");
                        await Task.Delay(200);
                    }

                    // Animate logo
                    var logoText = this.FindControl<TextBlock>("LogoText");
                    if (logoText != null)
                    {
                        logoText.Classes.Add("loaded");
                        await Task.Delay(300);
                    }

                    // Animate tabs
                    var tabListBox = this.FindControl<ListBox>("TabListBox");
                    if (tabListBox != null)
                    {
                        tabListBox.Classes.Add("loaded");
                        await Task.Delay(200);
                    }

                    // Animate content title
                    var contentTitle = this.FindControl<TextBlock>("contentTitle");
                    if (contentTitle != null)
                    {
                        contentTitle.Classes.Add("loaded");
                        await Task.Delay(150);
                    }

                    // Animate content cards
                    await AnimateContentCardsAsync();

                    // Animate right panel
                    await AnimateRightPanelAsync();

                    // Animate status bar and license status bar last
                    await Task.Delay(200);
                    await AnimateStatusBarAsync();
                    await Task.Delay(100);
                    await AnimateLicenseStatusBarAsync();
                });
            }
            catch (Exception ex)
            {
                // Silently handle animation failures
                System.Diagnostics.Debug.WriteLine($"MainWindow animation error: {ex.Message}");
            }
        }

        private async Task AnimateContentCardsAsync()
        {
            try
            {
                // Animate currently visible content
                await AnimateVisibleContentAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Content cards animation error: {ex.Message}");
            }
        }

        private async Task AnimateVisibleContentAsync()
        {
            try
            {
                // Check which content is currently visible and animate it
                if (functionsContent?.IsVisible == true)
                {
                    await AnimateFunctionsContentAsync();
                }
                else if (odinContent?.IsVisible == true)
                {
                    await AnimateOdinContentAsync();
                }
                else if (advancedContent?.IsVisible == true)
                {
                    await AnimateAdvancedContentAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Visible content animation error: {ex.Message}");
            }
        }

        private async Task AnimateFunctionsContentAsync()
        {
            try
            {
                var cardsToAnimate = new[]
                {
                    (this.FindControl<Border>("MtpCard"), this.FindControl<TextBlock>("MtpHeader"), 150),
                    (this.FindControl<Border>("AdbCard"), this.FindControl<TextBlock>("AdbHeader"), 200)
                };

                foreach (var (card, header, delay) in cardsToAnimate)
                {
                    if (card != null)
                    {
                        card.Classes.Add("loaded");
                        await Task.Delay(100);
                    }

                    if (header != null)
                    {
                        header.Classes.Add("loaded");
                        await Task.Delay(50);
                    }

                    // Animate function buttons in this card
                    await AnimateFunctionButtonsInCard(card);
                    await Task.Delay(delay);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Functions content animation error: {ex.Message}");
            }
        }

        private async Task AnimateOdinContentAsync()
        {
            try
            {
                var odinCard = this.FindControl<Border>("OdinCard");
                var odinHeader = this.FindControl<TextBlock>("OdinHeader");

                if (odinCard != null)
                {
                    odinCard.Classes.Add("loaded");
                    await Task.Delay(100);
                }

                if (odinHeader != null)
                {
                    odinHeader.Classes.Add("loaded");
                    await Task.Delay(50);
                }

                await AnimateFunctionButtonsInCard(odinCard);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Odin content animation error: {ex.Message}");
            }
        }

        private async Task AnimateAdvancedContentAsync()
        {
            try
            {
                var cardsToAnimate = new[]
                {
                    (this.FindControl<Border>("FirmwareCard"), this.FindControl<TextBlock>("FirmwareHeader"), 100),
                    (this.FindControl<Border>("FlashOptionsCard"), this.FindControl<TextBlock>("FlashOptionsHeader"), 100),
                    (this.FindControl<Border>("FlashActionsCard"), this.FindControl<TextBlock>("FlashActionsHeader"), 100)
                };

                foreach (var (card, header, delay) in cardsToAnimate)
                {
                    if (card != null)
                    {
                        card.Classes.Add("loaded");
                        await Task.Delay(80);
                    }

                    if (header != null)
                    {
                        header.Classes.Add("loaded");
                        await Task.Delay(50);
                    }

                    // Animate elements in this card
                    await AnimateAdvancedCardElements(card);
                    await Task.Delay(delay);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Advanced content animation error: {ex.Message}");
            }
        }

        private async Task AnimateAdvancedCardElements(Border? card)
        {
            try
            {
                if (card?.Child is StackPanel stackPanel)
                {
                    foreach (var child in stackPanel.Children)
                    {
                        if (child is Grid grid)
                        {
                            foreach (var gridChild in grid.Children)
                            {
                                if (gridChild is Button button || gridChild is CheckBox || gridChild is TextBox)
                                {
                                    gridChild.Classes.Add("loaded");
                                    await Task.Delay(30);
                                }
                            }
                        }
                        else if (child is StackPanel childStackPanel)
                        {
                            foreach (var stackChild in childStackPanel.Children)
                            {
                                if (stackChild is CheckBox checkBox)
                                {
                                    checkBox.Classes.Add("loaded");
                                    await Task.Delay(30);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Advanced card elements animation error: {ex.Message}");
            }
        }

        private async Task AnimateFunctionButtonsInCard(Border? card)
        {
            try
            {
                if (card?.Child is StackPanel stackPanel)
                {
                    foreach (var child in stackPanel.Children)
                    {
                        if (child is Avalonia.Controls.Primitives.UniformGrid uniformGrid)
                        {
                            foreach (var gridChild in uniformGrid.Children)
                            {
                                if (gridChild is Button button)
                                {
                                    button.Classes.Add("loaded");
                                    await Task.Delay(50); // Quick stagger for buttons
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Function buttons animation error: {ex.Message}");
            }
        }

        private async Task AnimateRightPanelAsync()
        {
            try
            {
                // Animate right panel border
                var rightPanelBorder = this.FindControl<Border>("RightPanelBorder");
                if (rightPanelBorder != null)
                {
                    rightPanelBorder.Classes.Add("loaded");
                    await Task.Delay(200);
                }

                // Animate device connection card
                var deviceConnectionCard = this.FindControl<Border>("DeviceConnectionCard");
                var deviceConnectionHeader = this.FindControl<TextBlock>("DeviceConnectionHeader");

                if (deviceConnectionCard != null)
                {
                    deviceConnectionCard.Classes.Add("loaded");
                    await Task.Delay(100);
                }

                if (deviceConnectionHeader != null)
                {
                    deviceConnectionHeader.Classes.Add("loaded");
                    await Task.Delay(150);
                }

                // Animate logs header
                var logsHeader = this.FindControl<TextBlock>("LogsHeader");
                if (logsHeader != null)
                {
                    logsHeader.Classes.Add("loaded");
                    await Task.Delay(100);
                }

                // Animate logs area
                var richLogs = this.FindControl<SelectableTextBlock>("Richlogs");
                richLogs?.Classes.Add("loaded");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Right panel animation error: {ex.Message}");
            }
        }

        private async Task AnimateStatusBarAsync()
        {
            try
            {
                var statusBar = this.FindControl<Border>("StatusBarBorder");
                if (statusBar != null)
                {
                    statusBar.Classes.Add("loaded");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Status bar animation error: {ex.Message}");
            }
        }

        private async Task AnimateLicenseStatusBarAsync()
        {
            try
            {
                var licenseStatusBar = this.FindControl<Border>("LicenseStatusBar");
                if (licenseStatusBar != null)
                {
                    licenseStatusBar.Classes.Add("loaded");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"License status bar animation error: {ex.Message}");
            }
        }

        private async Task CheckAndDisplayChangelogAsync()
        {
            try
            {
                var xmlContent = await _httpClient.GetStringAsync(UpdateUrl).ConfigureAwait(false);
                var xdoc = XDocument.Parse(xmlContent);

                var versionElement = xdoc.Descendants("version").FirstOrDefault();
                var changelogElement = xdoc.Descendants("changelog").FirstOrDefault();

                if (versionElement?.Value != null && changelogElement?.Value != null)
                {
                    string version = versionElement.Value;
                    string changelogUrl = changelogElement.Value;

                    if (!string.IsNullOrEmpty(changelogUrl))
                    {
                        string? changelogContent = await GetChangelogContentAsync(changelogUrl).ConfigureAwait(false);

                        if (!string.IsNullOrEmpty(changelogContent))
                        {
                            await DisplayChangelogInLogsAsync(version, changelogContent).ConfigureAwait(false);
                        }
                    }
                }
            }
            catch
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    RichLogs("Failed to load changelog information", ConvertColor(Colors.Gray), true, 12);
                });
            }
        }

        private async Task<string?> GetChangelogContentAsync(string changelogUrl)
        {
            try
            {
                return await _httpClient.GetStringAsync(changelogUrl).ConfigureAwait(false);
            }
            catch
            {
                return null;
            }
        }

        private async Task DisplayChangelogInLogsAsync(string version, string changelogContent)
        {
            await Dispatcher.UIThread.InvokeAsync(() =>
            {
                RichLogs(changelogContent, ConvertColor(Colors.Silver), true, 13);
            });
        }

        private static bool IsTokenValid()
        {
            return !string.IsNullOrEmpty(LoginWindow.AuthToken) &&
                   DateTime.UtcNow < LoginWindow.TokenExpiresAt;
        }

        private async Task<bool> CheckTokenBeforeOperationAsync()
        {
            if (!IsTokenValid())
            {
                await HandleTokenExpiredAsync().ConfigureAwait(false);
                return false;
            }
            return true;
        }

        private async Task HandleTokenExpiredAsync()
        {
            await Dispatcher.UIThread.InvokeAsync(async () =>
            {
                // Stop all timers and operations
                _licenseTimer?.Stop();
                _progressTimer?.Stop();
                
                // Cancel any running operations
                if (isOperationRunning)
                {
                    try
                    {
                        _currentOperationCts?.Cancel();
                        _flashCts?.Cancel();
                        ProcessManager.TerminateAllProcesses();
                    }
                    catch { }
                }

                // Show token expired dialog using AXAML
                var tokenExpiredWindow = new Views.TokenExpiredWindow();
                await tokenExpiredWindow.ShowDialog(this);
            });
        }

        // Test method for Samsung device detection - useful for debugging
        public async Task TestSamsungDetectionAsync()
        {
            try
            {
                ClearLog();
                RichLogs("Testing Samsung device detection...", ConvertColor(Colors.Yellow), true, 14);
                
                var testResults = await USB.TestSamsungDetectionAsync();
                RichLogs(testResults, ConvertColor(Colors.LightGray), true, 12);
                
                RichLogs("Detection test completed.", ConvertColor(Colors.Green), true, 14);
            }
            catch (Exception ex)
            {
                RichLogs($"Detection test failed: {ex.Message}", ConvertColor(Colors.Red), true);
            }
        }

        // Method to manually trigger USB detection update - useful for debugging
        public async Task RefreshUsbDetectionAsync()
        {
            try
            {
                RichLogs("Refreshing USB detection...", ConvertColor(Colors.Cyan), true);
                await USB.UpdateList();
                RichLogs("USB detection refreshed.", ConvertColor(Colors.Green), true);
            }
            catch (Exception ex)
            {
                RichLogs($"Failed to refresh USB detection: {ex.Message}", ConvertColor(Colors.Red), true);
            }
        }
    }
}