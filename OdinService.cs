﻿using SharpOdinClient;
using SharpOdinClient.structs;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static SamsungTool.Library.GUI.UI;

namespace SamsungTool.Library
{
    public enum PartitionType
    {
        AP, BL, CP, CSC, USERDATA
    }

    public class FlashEntry
    {
        public PartitionType Type { get; set; }
        public string? FilePath { get; set; }
    }

    public class FlashOptions
    {
        public bool AutoReboot { get; set; } = true;
        public bool ValidateMd5 { get; set; } = true;
        public bool NandErase { get; set; }
    }

    public class ProgressReport
    {
        public string? CurrentFile { get; set; }
        public long BytesTransferred { get; set; }
        public long TotalBytes { get; set; }
        public int PercentComplete { get; set; }
    }

    public sealed class OdinService : IDisposable
    {
        private readonly Odin _odin = new Odin();
        private IProgress<ProgressReport>? _currentProgress;
        private string _currentFlashingFile = "";
        private HashSet<string> _completedFiles = new HashSet<string>();
        private HashSet<string> _reportedMessages = new HashSet<string>();
        private Dictionary<string, string> _deviceInfo = new Dictionary<string, string>();
        private bool _isCollectingDeviceInfo = false;
        private string _currentInfoKey = "";
        private bool _deviceInfoPrinted = false;
        private Dictionary<string, bool> _fileCompleted = new Dictionary<string, bool>();
        private bool _disposed = false;

        public OdinService()
        {
            _odin.ProgressChanged += OnProgressChanged;
            _odin.Log += OnLog;
        }

        private void OnLog(string text, SharpOdinClient.util.utils.MsgType color, bool isError = false)
        {
            Color logColor = color == SharpOdinClient.util.utils.MsgType.Result ? Color.CornflowerBlue : Color.Silver;
            if (isError) logColor = Color.IndianRed;

            if (text.StartsWith("Flashing") && (text.EndsWith(":") || text.Contains("Flashing")))
            {
                return;
            }

            if (text.Trim() == ": Ok" || text.Trim() == " Completed" || text.Trim() == "Ok")
            {
                return;
            }

            if (ProcessDeviceInfo(text))
            {
                return;
            }

            if (_reportedMessages.Contains(text))
            {
                return;
            }

            if (text.Length < 200)
            {
                _reportedMessages.Add(text);
            }

            if (text.Contains("File partition cannot find"))
            {
                string cleanedText = text.Replace("\r", "").Replace("\n", " ");
                RichLogs(cleanedText, Color.Orange, true);
                return;
            }

            RichLogs(text, logColor, true);
        }

        private bool ProcessDeviceInfo(string text)
        {
            string[] deviceInfoLabels = new[] {
                "Model Number:", "Unique Id:", "Capa Number:", "vendor:",
                "Firmware Version:", "Product Id:", "Provision:", "Sales Code:",
                "Build Number:", "Did Number:", "Tmu Number:"
            };

            if (_deviceInfoPrinted)
            {
                if (deviceInfoLabels.Any(l => text.Contains(l)))
                {
                    return true;
                }
                return false;
            }

            foreach (string label in deviceInfoLabels)
            {
                if (text.Trim() == label)
                {
                    _isCollectingDeviceInfo = true;
                    _currentInfoKey = label.TrimEnd(':');
                    return true;
                }
            }

            if (_isCollectingDeviceInfo && !string.IsNullOrEmpty(_currentInfoKey))
            {
                string value = text.Trim();
                _deviceInfo[_currentInfoKey] = value;

                RichLogs($"{_currentInfoKey}: ", Color.Silver, false);
                RichLogs(value, Color.CornflowerBlue, true);

                _currentInfoKey = "";

                if (text.Trim() == deviceInfoLabels.Last().TrimEnd(':'))
                {
                    _isCollectingDeviceInfo = false;
                }
                return true;
            }

            return false;
        }

        private void OnProgressChanged(string filename, long max, long value, long writtenSize)
        {
            string normalizedFilename = filename.EndsWith(".lz4") ?
                filename.Substring(0, filename.Length - 4) : filename;
            string displayFilename = Path.GetFileName(normalizedFilename);

            if (normalizedFilename != _currentFlashingFile)
            {
                if (!string.IsNullOrEmpty(_currentFlashingFile) && !_completedFiles.Contains(_currentFlashingFile))
                {
                    RichLogs("Okay", Color.LimeGreen, true);
                    _completedFiles.Add(_currentFlashingFile);
                }

                _currentFlashingFile = normalizedFilename;
                RichLogs($"Flashing {displayFilename}: ", Color.Silver, false);
            }

            if (value >= max && max > 0)
            {
                if (!_fileCompleted.ContainsKey(normalizedFilename) || !_fileCompleted[normalizedFilename])
                {
                    _fileCompleted[normalizedFilename] = true;
                    RichLogs("Okay", Color.LimeGreen, true);
                    _completedFiles.Add(normalizedFilename);
                }
            }

            int percentComplete = max > 0 ? (int)((value * 100) / max) : 0;

            // Report progress for the progress bar and status updates
            _currentProgress?.Report(new ProgressReport
            {
                CurrentFile = displayFilename,
                TotalBytes = max,
                BytesTransferred = value,
                PercentComplete = percentComplete
            });
        }
        public async Task<IReadOnlyDictionary<PartitionType, string>> FlashAsync(
            string port,
            FlashOptions options,
            IEnumerable<FlashEntry> files,
            IProgress<ProgressReport> progress,
            CancellationToken token)
        {
            _currentProgress = progress;
            _currentFlashingFile = "";
            _completedFiles.Clear();
            _reportedMessages.Clear();
            _deviceInfo.Clear();
            _isCollectingDeviceInfo = false;
            _currentInfoKey = "";
            _deviceInfoPrinted = false;
            _fileCompleted.Clear();

            RichLogs("Connecting to device...", Color.Silver, false);
            if (!await _odin.FindAndSetDownloadMode())
            {
                RichLogs("Failed", Color.IndianRed, true);
                throw new InvalidOperationException("Device not found in download mode");
            }
            RichLogs("Okay", Color.LimeGreen, true);

            await _odin.PrintInfo();
            _deviceInfoPrinted = true;

            RichLogs("Checking ODIN mode...", Color.Silver, false);
            if (!await _odin.IsOdin())
            {
                RichLogs("Failed", Color.IndianRed, true);
                throw new InvalidOperationException("Device not in ODIN mode");
            }
            RichLogs("Okay", Color.LimeGreen, true);

            var flashFiles = new List<FileFlash>();
            long totalSize = 0;

            RichLogs("Processing firmware files...", Color.Silver, false);

            foreach (var file in files)
            {
                if (!File.Exists(file.FilePath))
                    continue;

                var tarInfo = _odin.tar.TarInformation(file.FilePath);
                foreach (var item in tarInfo)
                {
                    var fileFlash = new FileFlash
                    {
                        Enable = true,
                        FileName = item.Filename,
                        FilePath = file.FilePath,
                        RawSize = Path.GetExtension(item.Filename).ToLower() == ".lz4"
                            ? _odin.CalculateLz4SizeFromTar(file.FilePath, item.Filename)
                            : item.Filesize
                    };
                    flashFiles.Add(fileFlash);
                    totalSize += fileFlash.RawSize;
                }
            }
            RichLogs("Okay", Color.LimeGreen, true);

            RichLogs("Initializing LOKE protocol...", Color.Silver, false);
            if (!await _odin.LOKE_Initialize(totalSize))
            {
                RichLogs("Failed", Color.IndianRed, true);
                throw new InvalidOperationException("Failed to initialize LOKE");
            }
            RichLogs("Okay", Color.LimeGreen, true);

            RichLogs("Reading PIT from device...", Color.Silver, false);
            var pitResult = await _odin.Read_Pit();
            if (!pitResult.Result)
            {
                RichLogs("Failed", Color.IndianRed, true);
                throw new InvalidOperationException("Failed to read PIT");
            }
            RichLogs("Okay", Color.LimeGreen, true);

            RichLogs("", Color.Silver, true);
            RichLogs("Starting firmware flash...", Color.Silver, true);

            if (!await _odin.FlashFirmware(flashFiles, pitResult.Pit, 0, 1, true))
            {
                RichLogs("Flash operation failed", Color.IndianRed, true);
                throw new InvalidOperationException("Flash failed");
            }

            if (!string.IsNullOrEmpty(_currentFlashingFile) && !_completedFiles.Contains(_currentFlashingFile))
            {
                RichLogs("Okay", Color.LimeGreen, true);
                _completedFiles.Add(_currentFlashingFile);
            }

            if (options.AutoReboot)
            {
                RichLogs("", Color.Silver, true);
                RichLogs("Rebooting device...", Color.Silver, false);
                await _odin.PDAToNormal();
                RichLogs("Okay", Color.LimeGreen, true);
            }

            var result = new Dictionary<PartitionType, string>();
            foreach (var file in files)
            {
                result[file.Type] = "Success";
            }

            return result;
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            // Unsubscribe event handlers
            if (_odin != null)
            {
                _odin.ProgressChanged -= OnProgressChanged;
                _odin.Log -= OnLog;

                // If Odin implements IDisposable, dispose it
                if (_odin is IDisposable disposableOdin)
                {
                    disposableOdin.Dispose();
                }
            }

            // Clear collections
            _completedFiles?.Clear();
            _reportedMessages?.Clear();
            _deviceInfo?.Clear();
            _fileCompleted?.Clear();

            // Mark as disposed
            _disposed = true;
        }
    }
}